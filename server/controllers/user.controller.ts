import { Context } from "hono";
import { errorResponse, successResponse } from "../utils/response.util";
import userService from "../services/user.service";

class UserController {
  async updateProfile(c: Context) {
    try {
      const user = c.get("user");
      if (!user) {
        return c.json(errorResponse("User not found"), 404);
      }

      const body = await c.req.json();
      const {
        firstName,
        lastName,
        phoneNumber,
        sellerAddressName,
        sellerCountry,
        sellerProvince,
        sellerCity,
        sellerPostalCode,
        sellerFullAddress
      } = body;

      const updatedProfile = await userService.updateProfile(user.id, {
        firstName,
        lastName,
        phoneNumber,
        sellerAddressName,
        sellerCountry,
        sellerProvince,
        sellerCity,
        sellerPostalCode,
        sellerFullAddress,
      });

      if (!updatedProfile.status) {
        return c.json(updatedProfile, 400);
      }

      return c.json(successResponse("Profile updated successfully", updatedProfile.data), 200);
    } catch (error) {
      return c.json(
        errorResponse(`${(error as Error).message || "An error occurred"}`),
        500
      );
    }
  }

  async getSellerStatus(c: Context) {
    try {
      const user = c.get("user");
      if (!user) {
        return c.json(errorResponse("User not found"), 404);
      }

      const sellerStatus = await userService.getSellerStatus(user.id);

      if (!sellerStatus.status) {
        return c.json(sellerStatus, 400);
      }

      return c.json(successResponse("Seller status retrieved successfully", sellerStatus.data), 200);
    } catch (error) {
      return c.json(
        errorResponse(`${(error as Error).message || "An error occurred"}`),
        500
      );
    }
  }

  async requestVerification(c: Context) {
    try {
      const user = c.get("user");
      if (!user) {
        return c.json(errorResponse("User not found"), 404);
      }

      const body = await c.req.json();
      const { type, value } = body;

      if (!type || !value) {
        return c.json(errorResponse("Type and value are required"), 400);
      }

      if (type !== 'email') {
        return c.json(errorResponse("Type must be 'email'"), 400);
      }

      const verification = await userService.requestVerification(user.id, type, value);

      if (!verification.status) {
        return c.json(verification, 400);
      }

      return c.json(successResponse("Verification code sent successfully", verification.data), 200);
    } catch (error) {
      return c.json(
        errorResponse(`${(error as Error).message || "An error occurred"}`),
        500
      );
    }
  }

  async confirmVerification(c: Context) {
    try {
      const user = c.get("user");
      if (!user) {
        return c.json(errorResponse("User not found"), 404);
      }

      const body = await c.req.json();
      const { type, code } = body;

      if (!type || !code) {
        return c.json(errorResponse("Type and code are required"), 400);
      }

      if (type !== 'email') {
        return c.json(errorResponse("Type must be 'email'"), 400);
      }

      const verification = await userService.confirmVerification(user.id, type, code);

      if (!verification.status) {
        return c.json(verification, 400);
      }

      return c.json(successResponse("Verification completed successfully", verification.data), 200);
    } catch (error) {
      return c.json(
        errorResponse(`${(error as Error).message || "An error occurred"}`),
        500
      );
    }
  }

  async getUserAddresses(c: Context) {
    try {
      const user = c.get("user");
      if (!user) {
        return c.json(errorResponse("User not found"), 404);
      }

      const addresses = await userService.getUserAddresses(user.id);

      if (!addresses.status) {
        return c.json(addresses, 400);
      }

      return c.json(successResponse("Addresses retrieved successfully", addresses.data), 200);
    } catch (error) {
      return c.json(
        errorResponse(`${(error as Error).message || "An error occurred"}`),
        500
      );
    }
  }

  async createUserAddress(c: Context) {
    try {
      const user = c.get("user");
      if (!user) {
        return c.json(errorResponse("User not found"), 404);
      }

      const body = await c.req.json();
      const {
        name,
        country,
        province,
        city,
        postalCode,
        fullAddress,
        isDefault
      } = body;

      const address = await userService.createUserAddress(user.id, {
        name,
        country,
        province,
        city,
        postalCode,
        fullAddress,
        isDefault
      });

      if (!address.status) {
        return c.json(address, 400);
      }

      return c.json(successResponse("Address created successfully", address.data), 201);
    } catch (error) {
      return c.json(
        errorResponse(`${(error as Error).message || "An error occurred"}`),
        500
      );
    }
  }
}

const userController = new UserController();
export default userController;
