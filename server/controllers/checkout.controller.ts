import { Context } from "hono";
import { errorResponse } from "../utils/response.util";
import checkoutService from "../services/checkout.service";

class CheckoutController {
  async validateCheckout(c: Context) {
    try {
      const body = await c.req.json();
      const user = c.get('user');

      if (!user) {
        return c.json(errorResponse("Authentication required", { type: 'authentication' }), 401);
      }

      const { checkoutType, productId, bidId } = body;

      // Input validation
      if (!checkoutType) {
        return c.json(errorResponse("Checkout type is required", { type: 'validation' }), 400);
      }

      if (!['cart', 'buy-now', 'bidding'].includes(checkoutType)) {
        return c.json(errorResponse("Invalid checkout type. Must be 'cart', 'buy-now', or 'bidding'", { type: 'validation' }), 400);
      }

      if ((checkoutType === 'buy-now' || checkoutType === 'bidding') && !productId) {
        return c.json(errorResponse(`Product ID is required for ${checkoutType} checkout`, { type: 'validation' }), 400);
      }

      const result = await checkoutService.validateCheckoutAccess(
        user.id,
        checkoutType,
        productId,
        bidId
      );

      // Return appropriate HTTP status based on error type
      if (!result.status) {
        const errorType = result.message;
        let statusCode = 400;

        switch (errorType) {
          case 'authentication':
            statusCode = 401;
            break;
          case 'validation':
            statusCode = 422;
            break;
          case 'inventory':
            statusCode = 409;
            break;
          case 'network':
            statusCode = 503;
            break;
          default:
            statusCode = 400;
        }

        return c.json(result, statusCode as any);
      }

      return c.json(result, 200);
    } catch (error) {
      console.error("Validate checkout controller error:", error);
      return c.json(
        errorResponse("Internal server error", {
          type: 'unknown',
          details: process.env.NODE_ENV === 'development' ? error : undefined
        }),
        500
      );
    }
  }

  // Shipping Address Controllers
  async createShippingAddress(c: Context) {
    try {
      const body = await c.req.json();
      const user = c.get('user');

      if (!user) {
        return c.json(errorResponse("Unauthorized"), 401);
      }

      const result = await checkoutService.createShippingAddress(user.id, body);

      if (!result.status) {
        return c.json(result, 400);
      }

      return c.json(result, 201);
    } catch (error) {
      console.error("Create shipping address controller error:", error);
      return c.json(
        errorResponse(`${(error as Error).message || "An error occurred"}`),
        500
      );
    }
  }

  async getShippingAddresses(c: Context) {
    try {
      const user = c.get('user');

      if (!user) {
        return c.json(errorResponse("Unauthorized"), 401);
      }

      const result = await checkoutService.getShippingAddresses(user.id);

      if (!result.status) {
        return c.json(result, 400);
      }

      return c.json(result, 200);
    } catch (error) {
      console.error("Get shipping addresses controller error:", error);
      return c.json(
        errorResponse(`${(error as Error).message || "An error occurred"}`),
        500
      );
    }
  }

  async updateShippingAddress(c: Context) {
    try {
      const addressId = c.req.param('addressId');
      const body = await c.req.json();
      const user = c.get('user');

      if (!user) {
        return c.json(errorResponse("Unauthorized"), 401);
      }

      const result = await checkoutService.updateShippingAddress(user.id, addressId, body);

      if (!result.status) {
        return c.json(result, 400);
      }

      return c.json(result, 200);
    } catch (error) {
      console.error("Update shipping address controller error:", error);
      return c.json(
        errorResponse(`${(error as Error).message || "An error occurred"}`),
        500
      );
    }
  }

  async deleteShippingAddress(c: Context) {
    try {
      const addressId = c.req.param('addressId');
      const user = c.get('user');

      if (!user) {
        return c.json(errorResponse("Unauthorized"), 401);
      }

      const result = await checkoutService.deleteShippingAddress(user.id, addressId);

      if (!result.status) {
        return c.json(result, 400);
      }

      return c.json(result, 200);
    } catch (error) {
      console.error("Delete shipping address controller error:", error);
      return c.json(
        errorResponse(`${(error as Error).message || "An error occurred"}`),
        500
      );
    }
  }

  // Order Controllers
  async createOrderFromCart(c: Context) {
    try {
      const body = await c.req.json();
      const user = c.get('user');

      if (!user) {
        return c.json(errorResponse("Unauthorized"), 401);
      }

      const result = await checkoutService.createOrderFromCart(user.id, body);

      if (!result.status) {
        return c.json(result, 400);
      }

      return c.json(result, 201);
    } catch (error) {
      console.error("Create order from cart controller error:", error);
      return c.json(
        errorResponse(`${(error as Error).message || "An error occurred"}`),
        500
      );
    }
  }

  async createOrder(c: Context) {
    try {
      const body = await c.req.json();
      const user = c.get('user');

      if (!user) {
        return c.json(errorResponse("Authentication required", { type: 'authentication' }), 401);
      }

      // Input validation
      if (!body.orderType) {
        return c.json(errorResponse("Order type is required", { type: 'validation' }), 400);
      }

      if (!['cart', 'buy-now', 'bidding'].includes(body.orderType)) {
        return c.json(errorResponse("Invalid order type", { type: 'validation' }), 400);
      }

      if ((body.orderType === 'buy-now' || body.orderType === 'bidding') && !body.productId) {
        return c.json(errorResponse(`Product ID is required for ${body.orderType} orders`, { type: 'validation' }), 400);
      }

      if (body.orderType === 'bidding' && !body.bidId) {
        return c.json(errorResponse("Bid ID is required for bidding orders", { type: 'validation' }), 400);
      }

      const result = await checkoutService.createOrder(user.id, body);

      if (!result.status) {
        return c.json(result, 400 as any);
      }

      return c.json(result, 201 as any);
    } catch (error) {
      console.error("Order creation error:", error);
      return c.json(
        errorResponse("Failed to create order", {
          type: 'unknown',
          details: process.env.NODE_ENV === 'development' ? error : undefined
        }),
        500
      );
    }
  }

  async createBuyNowOrder(c: Context) {
    try {
      const body = await c.req.json();
      const user = c.get('user');

      if (!user) {
        return c.json(errorResponse("Unauthorized"), 401);
      }

      const result = await checkoutService.createBuyNowOrder(user.id, body);

      if (!result.status) {
        return c.json(result, 400);
      }

      return c.json(result, 201);
    } catch (error) {
      console.error("Create buy now order controller error:", error);
      return c.json(
        errorResponse(`${(error as Error).message || "An error occurred"}`),
        500
      );
    }
  }

  async getOrders(c: Context) {
    try {
      const rawQuery = c.req.query();
      const query = {
        page: parseInt(rawQuery.page || '1'),
        limit: parseInt(rawQuery.limit || '10'),
        status: rawQuery.status,
        paymentStatus: rawQuery.paymentStatus,
        sortBy: rawQuery.sortBy || 'createdAt',
        sortOrder: rawQuery.sortOrder || 'desc'
      };

      const user = c.get('user');

      if (!user) {
        return c.json(errorResponse("Unauthorized"), 401);
      }

      const result = await checkoutService.getOrders(user.id, query);

      if (!result.status) {
        return c.json(result, 400);
      }

      return c.json(result, 200);
    } catch (error) {
      console.error("Get orders controller error:", error);
      return c.json(
        errorResponse(`${(error as Error).message || "An error occurred"}`),
        500
      );
    }
  }

  async getOrder(c: Context) {
    try {
      const orderId = c.req.param('orderId');
      const user = c.get('user');

      if (!user) {
        return c.json(errorResponse("Unauthorized"), 401);
      }

      const result = await checkoutService.getOrder(user.id, orderId);

      if (!result.status) {
        return c.json(result, 404);
      }

      return c.json(result, 200);
    } catch (error) {
      console.error("Get order controller error:", error);
      return c.json(
        errorResponse(`${(error as Error).message || "An error occurred"}`),
        500
      );
    }
  }

  async updateOrderStatus(c: Context) {
    try {
      const orderId = c.req.param('orderId');
      const body = await c.req.json();
      const user = c.get('user');

      if (!user) {
        return c.json(errorResponse("Unauthorized"), 401);
      }

      // For now, only allow admin users to update order status
      if (user.role !== 'admin') {
        return c.json(errorResponse("Forbidden - Admin access required"), 403);
      }

      const result = await checkoutService.updateOrderStatus(orderId, body);

      if (!result.status) {
        return c.json(result, 400);
      }

      return c.json(result, 200);
    } catch (error) {
      console.error("Update order status controller error:", error);
      return c.json(
        errorResponse(`${(error as Error).message || "An error occurred"}`),
        500
      );
    }
  }
}

const checkoutController = new CheckoutController();
export default checkoutController;
