import { PrismaClient } from "../../generated/client";
import { errorResponse, successResponse } from "../utils/response.util";
import crypto from "crypto";

const prisma = new PrismaClient();

interface UpdateProfileData {
  firstName: string;
  lastName: string;
  phoneNumber: string;
  sellerAddressName?: string;
  sellerCountry?: string;
  sellerProvince?: string;
  sellerCity?: string;
  sellerPostalCode?: string;
  sellerFullAddress?: string;
}

interface CreateUserAddressData {
  name: string;
  country: string;
  province: string;
  city: string;
  postalCode: string;
  fullAddress: string;
  isDefault?: boolean;
}

class UserService {
  async updateProfile(userId: string, data: UpdateProfileData) {
    try {
      const {
        firstName,
        lastName,
        phoneNumber,
        sellerAddressName,
        sellerCountry,
        sellerProvince,
        sellerCity,
        sellerPostalCode,
        sellerFullAddress
      } = data;

      // Check if user exists
      const existingUser = await prisma.user.findUnique({
        where: { id: userId }
      });

      if (!existingUser) {
        return errorResponse("User not found");
      }

      // Check if phone number is already taken by another user
      if (phoneNumber && phoneNumber !== existingUser.phoneNumber) {
        const phoneExists = await prisma.user.findFirst({
          where: {
            phoneNumber: phoneNumber,
            id: { not: userId }
          }
        });

        if (phoneExists) {
          return errorResponse("Phone number is already in use");
        }
      }

      // Check if seller address is being completed for the first time
      const isCompletingAddress = sellerAddressName && sellerCountry && sellerProvince &&
        sellerCity && sellerPostalCode && sellerFullAddress && !existingUser.sellerAddressComplete;

      // Update user profile
      const updatedUser = await prisma.user.update({
        where: { id: userId },
        data: {
          firstName,
          lastName,
          phoneNumber,
          ...(sellerAddressName !== undefined && { sellerAddressName }),
          ...(sellerCountry !== undefined && { sellerCountry }),
          ...(sellerProvince !== undefined && { sellerProvince }),
          ...(sellerCity !== undefined && { sellerCity }),
          ...(sellerPostalCode !== undefined && { sellerPostalCode }),
          ...(sellerFullAddress !== undefined && { sellerFullAddress }),
          ...(isCompletingAddress && { sellerAddressComplete: true }),
          updatedAt: new Date(),
        },
        select: {
          id: true,
          firstName: true,
          lastName: true,
          email: true,
          phoneNumber: true,
          sellerAddressName: true,
          sellerCountry: true,
          sellerProvince: true,
          sellerCity: true,
          sellerPostalCode: true,
          sellerFullAddress: true,
          sellerAddressComplete: true,
          isEmailVerified: true,
          createdAt: true,
          updatedAt: true,
        }
      });

      return successResponse("Profile updated successfully", updatedUser);
    } catch (error) {
      throw new Error(`Profile update failed: ${(error as Error).message}`);
    }
  }

  async getSellerStatus(userId: string) {
    try {
      const user = await prisma.user.findUnique({
        where: { id: userId },
        select: {
          isEmailVerified: true,
          sellerAddressComplete: true,
        }
      });

      if (!user) {
        return errorResponse("User not found");
      }

      const emailVerified = user.isEmailVerified || false;
      const hasSellerAddress = user.sellerAddressComplete || false;

      const canSell = emailVerified && hasSellerAddress;

      const missingRequirements: string[] = [];
      if (!emailVerified) missingRequirements.push("Email verification");
      if (!hasSellerAddress) missingRequirements.push("Complete seller address");

      const sellerStatus = {
        canSell,
        emailVerified,
        hasSellerAddress,
        missingRequirements,
      };

      return successResponse("Seller status retrieved successfully", sellerStatus);
    } catch (error) {
      throw new Error(`Seller status retrieval failed: ${(error as Error).message}`);
    }
  }

  async requestVerification(userId: string, type: 'email', value: string) {
    try {
      // Generate 6-digit verification code
      const code = crypto.randomInt(100000, 999999).toString();
      const expiresAt = new Date(Date.now() + 10 * 60 * 1000); // 10 minutes

      // Delete any existing verification codes for this user and type
      await prisma.verificationCode.deleteMany({
        where: {
          userId,
          type,
        }
      });

      // Create new verification code
      await prisma.verificationCode.create({
        data: {
          userId,
          type,
          value,
          code,
          expiresAt,
          verified: false,
        }
      });

      // TODO: Send verification code via email
      console.log(`Email verification code for ${value}: ${code}`);
      // Implement email sending logic here

      return successResponse("Verification code sent successfully", {
        type,
        expiresAt,
        message: `Verification code sent to your email`
      });
    } catch (error) {
      throw new Error(`Verification request failed: ${(error as Error).message}`);
    }
  }

  async confirmVerification(userId: string, type: 'email', code: string) {
    try {
      // Find verification code
      const verificationCode = await prisma.verificationCode.findFirst({
        where: {
          userId,
          type,
          code,
          verified: false,
          expiresAt: {
            gt: new Date()
          }
        }
      });

      if (!verificationCode) {
        return errorResponse("Invalid or expired verification code");
      }

      // Mark verification code as used
      await prisma.verificationCode.update({
        where: { id: verificationCode.id },
        data: { verified: true }
      });

      // Update user email verification status
      await prisma.user.update({
        where: { id: userId },
        data: { isEmailVerified: true }
      });

      return successResponse("Verification completed successfully", {
        type,
        verified: true
      });
    } catch (error) {
      throw new Error(`Verification confirmation failed: ${(error as Error).message}`);
    }
  }

  async getUserAddresses(userId: string) {
    try {
      const addresses = await prisma.userAddress.findMany({
        where: { userId },
        orderBy: [
          { isDefault: 'desc' },
          { createdAt: 'desc' }
        ]
      });

      return successResponse("Addresses retrieved successfully", addresses);
    } catch (error) {
      throw new Error(`Get user addresses failed: ${(error as Error).message}`);
    }
  }

  async createUserAddress(userId: string, data: CreateUserAddressData) {
    try {
      const {
        name,
        country,
        province,
        city,
        postalCode,
        fullAddress,
        isDefault = false
      } = data;

      // If this is set as default, unset other default addresses
      if (isDefault) {
        await prisma.userAddress.updateMany({
          where: {
            userId,
            isDefault: true
          },
          data: { isDefault: false }
        });
      }

      // If this is the first address, make it default
      const existingAddressCount = await prisma.userAddress.count({
        where: { userId }
      });

      const shouldBeDefault = isDefault || existingAddressCount === 0;

      const address = await prisma.userAddress.create({
        data: {
          userId,
          name,
          country,
          province,
          city,
          postalCode,
          fullAddress,
          isDefault: shouldBeDefault,
        }
      });

      return successResponse("Address created successfully", address);
    } catch (error) {
      throw new Error(`Create user address failed: ${(error as Error).message}`);
    }
  }
}

const userService = new UserService();
export default userService;
