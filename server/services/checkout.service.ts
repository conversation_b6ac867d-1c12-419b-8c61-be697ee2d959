import { prisma } from "../db";
import { successResponse, errorResponse } from "../utils/response.util";
import shippingService from './shipping.service';
import { getJakartaTime } from "../utils/timezone.util";

class CheckoutService {
  // Generate unique order number
  private generateOrderNumber(): string {
    const timestamp = Date.now().toString();
    const random = Math.random().toString(36).substring(2, 8).toUpperCase();
    return `ORD-${timestamp}-${random}`;
  }

  async validateCheckoutAccess(userId: string, checkoutType: string, productId?: string, bidId?: string) {
    try {
      // Input validation
      if (!userId) {
        return errorResponse("User ID is required", { type: 'validation' });
      }

      if (!['cart', 'buy-now', 'bidding'].includes(checkoutType)) {
        return errorResponse("Invalid checkout type. Must be 'cart', 'buy-now', or 'bidding'", { type: 'validation' });
      }

      switch (checkoutType) {
        case 'cart':
          return await this.validateCartCheckout(userId);

        case 'buy-now':
          if (!productId) {
            return errorResponse("Product ID is required for buy-now checkout", { type: 'validation' });
          }
          return await this.validateBuyNowCheckout(userId, productId);

        case 'bidding':
          if (!productId) {
            return errorResponse("Product ID is required for bidding checkout", { type: 'validation' });
          }
          return await this.validateBiddingCheckout(userId, productId, bidId);

        default:
          return errorResponse("Invalid checkout type", { type: 'validation' });
      }
    } catch (error) {
      console.error("Validate checkout access error:", error);
      return errorResponse("Failed to validate checkout access", {
        type: 'unknown',
        details: process.env.NODE_ENV === 'development' ? error : undefined
      });
    }
  }

  private async validateCartCheckout(userId: string) {
    try {
      const cart = await prisma.cart.findUnique({
        where: { userId },
        include: {
          items: {
            include: {
              product: {
                include: {
                  images: true
                }
              }
            }
          }
        }
      });

      if (!cart || cart.items.length === 0) {
        return errorResponse("Your cart is empty. Please add items before checkout.", {
          type: 'validation',
          checkoutType: 'cart'
        });
      }

      // Validate each cart item
      const invalidItems = [];
      const validItems = [];

      for (const item of cart.items) {
        if (item.product.status !== 'active') {
          invalidItems.push({
            productId: item.product.id,
            itemName: item.product.itemName,
            reason: 'Product is no longer available'
          });
        } else if (item.product.sellType !== 'buy-now') {
          invalidItems.push({
            productId: item.product.id,
            itemName: item.product.itemName,
            reason: 'Product is only available through auction'
          });
        } else {
          validItems.push(item);
        }
      }

      if (invalidItems.length > 0) {
        return errorResponse("Some items in your cart are no longer available for purchase", {
          type: 'inventory',
          checkoutType: 'cart',
          invalidItems,
          validItemsCount: validItems.length
        });
      }

      // Calculate totals
      const totalItems = cart.items.reduce((sum, item) => sum + item.quantity, 0);
      const totalPrice = cart.items.reduce((sum, item) => sum + (Number(item.price) * item.quantity), 0);

      // Build checkout context
      const checkoutContext = {
        type: 'cart' as const,
        items: cart.items.map(item => ({
          id: item.id,
          productId: item.productId,
          quantity: item.quantity,
          price: item.price,
          product: {
            id: item.product.id,
            itemName: item.product.itemName,
            slug: item.product.slug || '',
            priceUSD: Number(item.product.priceUSD),
            images: item.product.images.map(img => ({
              id: img.id,
              imageUrl: img.imageUrl,
              isMain: img.isMain
            })),
            sellType: item.product.sellType as 'buy-now',
            status: item.product.status
          }
        })),
        totalItems,
        totalPrice
      };

      return successResponse("Cart checkout validated successfully", {
        checkoutType: 'cart',
        context: checkoutContext
      });
    } catch (error) {
      console.error("Cart checkout validation error:", error);
      return errorResponse("Failed to validate cart checkout", {
        type: 'unknown',
        checkoutType: 'cart'
      });
    }
  }

  private async validateBuyNowCheckout(_userId: string, productId: string) {
    try {
      const product = await prisma.product.findUnique({
        where: { id: productId },
        include: {
          images: true,
          seller: {
            select: {
              id: true,
              firstName: true,
              lastName: true
            }
          }
        }
      });

      if (!product) {
        return errorResponse("Product not found. It may have been removed or is no longer available.", {
          type: 'validation',
          checkoutType: 'buy-now',
          productId
        });
      }

      if (product.status !== 'active') {
        return errorResponse("This product is no longer available for purchase.", {
          type: 'inventory',
          checkoutType: 'buy-now',
          productId,
          productStatus: product.status
        });
      }

      if (product.sellType !== 'buy-now') {
        return errorResponse("This product is only available through auction bidding.", {
          type: 'validation',
          checkoutType: 'buy-now',
          productId,
          actualSellType: product.sellType
        });
      }

      // Build checkout context
      const checkoutContext = {
        type: 'buy-now' as const,
        productId: product.id,
        product: {
          id: product.id,
          itemName: product.itemName,
          slug: product.slug || '',
          priceUSD: Number(product.priceUSD),
          images: product.images.map(img => ({
            id: img.id,
            imageUrl: img.imageUrl,
            isMain: img.isMain
          })),
          sellType: product.sellType as 'buy-now',
          status: product.status
        },
        quantity: 1
      };

      return successResponse("Buy-now checkout validated successfully", {
        checkoutType: 'buy-now',
        context: checkoutContext
      });
    } catch (error) {
      console.error("Buy-now checkout validation error:", error);
      return errorResponse("Failed to validate buy-now checkout", {
        type: 'unknown',
        checkoutType: 'buy-now',
        productId
      });
    }
  }

  private async validateBiddingCheckout(userId: string, productId: string, _bidId?: string) {
    try {
      const now = getJakartaTime();

      // Get product details with winner information
      const product = await prisma.product.findUnique({
        where: { id: productId },
        include: {
          images: true,
          winner: {
            select: {
              id: true,
              firstName: true,
              lastName: true
            }
          },
          winnerBid: true,
          bids: {
            orderBy: { amount: 'desc' },
            take: 1,
            include: {
              bidder: {
                select: {
                  id: true,
                  firstName: true,
                  lastName: true
                }
              }
            }
          }
        }
      });

      if (!product) {
        return errorResponse("Auction product not found. It may have been removed.", {
          type: 'validation',
          checkoutType: 'bidding',
          productId
        });
      }

      if (product.sellType !== 'auction') {
        return errorResponse("This product is not an auction item. Please use buy-now checkout instead.", {
          type: 'validation',
          checkoutType: 'bidding',
          productId,
          actualSellType: product.sellType
        });
      }

      // Check if auction has ended
      if (!product.auctionEndDate || new Date(product.auctionEndDate) > now) {
        return errorResponse("This auction is still ongoing. Please wait for the auction to end.", {
          type: 'validation',
          checkoutType: 'bidding',
          productId,
          auctionEndDate: product.auctionEndDate,
          isOngoing: true
        });
      }

      // Check if auction is completed
      if (!product.auctionCompleted) {
        return errorResponse("This auction has ended but is still being processed. Please check back in a few minutes.", {
          type: 'validation',
          checkoutType: 'bidding',
          productId,
          auctionEndDate: product.auctionEndDate,
          isProcessing: true
        });
      }

      // Check if there's a winner set in the product
      if (!product.winnerId || !product.winnerBidId) {
        return errorResponse("No winner has been determined for this auction yet.", {
          type: 'validation',
          checkoutType: 'bidding',
          productId,
          noWinner: true
        });
      }

      // Verify user is the winner using the winnerId field
      if (product.winnerId !== userId) {
        return errorResponse("You are not the winner of this auction. Only the winning bidder can checkout.", {
          type: 'validation',
          checkoutType: 'bidding',
          productId,
          winnerId: product.winnerId,
          notWinner: true
        });
      }

      // Get the winning bid (fallback to bids if winnerBid relation is not loaded)
      const winningBid = product.winnerBid || product.bids[0];
      if (!winningBid) {
        return errorResponse("No winning bid found for this auction.", {
          type: 'validation',
          checkoutType: 'bidding',
          productId,
          noWinningBid: true
        });
      }

      // Calculate payment deadline (3 days after auction end)
      const auctionEndDate = new Date(product.auctionEndDate);
      const paymentDeadline = new Date(auctionEndDate.getTime() + (3 * 24 * 60 * 60 * 1000));

      // Check if payment deadline has expired
      if (now > paymentDeadline) {
        return errorResponse("Payment deadline has expired. You can no longer checkout for this auction.", {
          type: 'validation',
          checkoutType: 'bidding',
          productId,
          expired: true,
          paymentDeadline: paymentDeadline.toISOString(),
          auctionEndDate: auctionEndDate.toISOString()
        });
      }

      // Check if user already has an order for this auction
      const existingOrder = await prisma.order.findFirst({
        where: {
          userId,
          OR: [
            { bidId: winningBid.id },
            {
              items: {
                some: {
                  productId,
                  bidId: winningBid.id
                }
              }
            }
          ]
        },
        include: {
          items: true
        }
      });

      if (existingOrder) {
        return errorResponse("You already have an order for this auction. Please complete the payment for your existing order.", {
          type: 'validation',
          checkoutType: 'bidding',
          productId,
          existingOrder: {
            id: existingOrder.id,
            orderNumber: existingOrder.orderNumber,
            status: existingOrder.status,
            paymentStatus: existingOrder.paymentStatus,
            total: Number(existingOrder.total),
            currency: existingOrder.currency
          }
        });
      }

      // Calculate time remaining for payment
      const timeRemainingMs = paymentDeadline.getTime() - now.getTime();
      const hoursRemaining = Math.max(0, Math.floor(timeRemainingMs / (1000 * 60 * 60)));

      // Build checkout context
      const checkoutContext = {
        type: 'bidding' as const,
        productId: product.id,
        bidId: winningBid.id,
        product: {
          id: product.id,
          itemName: product.itemName,
          slug: product.slug || '',
          priceUSD: Number(product.priceUSD),
          images: product.images.map(img => ({
            id: img.id,
            imageUrl: img.imageUrl,
            isMain: img.isMain
          })),
          sellType: product.sellType as 'auction',
          status: product.status,
          auctionEndDate: product.auctionEndDate,
          auctionCompleted: product.auctionCompleted,
          winnerId: product.winnerId,
          winnerBidId: product.winnerBidId
        },
        winningBid: {
          id: winningBid.id,
          amount: Number(winningBid.amount),
          bidderId: winningBid.bidderId,
          bidder: (winningBid as any).bidder || product.winner
        },
        paymentDeadline: paymentDeadline.toISOString(),
        hoursRemaining,
        auctionEndDate: auctionEndDate.toISOString()
      };

      return successResponse("Bidding checkout validated successfully", {
        checkoutType: 'bidding',
        context: checkoutContext
      });
    } catch (error) {
      console.error("Bidding checkout validation error:", error);
      return errorResponse("Failed to validate bidding checkout", {
        type: 'unknown',
        checkoutType: 'bidding',
        productId
      });
    }
  }

  // Create shipping address
  async createShippingAddress(userId: string, addressData: any) {
    try {
      // If this is set as default, unset other default addresses
      if (addressData.isDefault) {
        await prisma.shippingAddress.updateMany({
          where: { userId, isDefault: true },
          data: { isDefault: false }
        });
      }

      const address = await prisma.shippingAddress.create({
        data: {
          ...addressData,
          userId
        }
      });

      return successResponse("Shipping address created successfully", address);
    } catch (error) {
      console.error("Create shipping address service error:", error);
      return errorResponse("Failed to create shipping address");
    }
  }

  // Get user's shipping addresses
  async getShippingAddresses(userId: string) {
    try {
      const addresses = await prisma.shippingAddress.findMany({
        where: { userId },
        orderBy: [
          { isDefault: 'desc' },
          { createdAt: 'desc' }
        ]
      });

      return successResponse("Shipping addresses retrieved successfully", addresses);
    } catch (error) {
      console.error("Get shipping addresses service error:", error);
      return errorResponse("Failed to retrieve shipping addresses");
    }
  }

  // Update shipping address
  async updateShippingAddress(userId: string, addressId: string, addressData: any) {
    try {
      // Verify address belongs to user
      const existingAddress = await prisma.shippingAddress.findFirst({
        where: { id: addressId, userId }
      });

      if (!existingAddress) {
        return errorResponse("Shipping address not found");
      }

      // If this is set as default, unset other default addresses
      if (addressData.isDefault) {
        await prisma.shippingAddress.updateMany({
          where: { userId, isDefault: true, id: { not: addressId } },
          data: { isDefault: false }
        });
      }

      const address = await prisma.shippingAddress.update({
        where: { id: addressId },
        data: addressData
      });

      return successResponse("Shipping address updated successfully", address);
    } catch (error) {
      console.error("Update shipping address service error:", error);
      return errorResponse("Failed to update shipping address");
    }
  }

  // Delete shipping address
  async deleteShippingAddress(userId: string, addressId: string) {
    try {
      // Verify address belongs to user
      const existingAddress = await prisma.shippingAddress.findFirst({
        where: { id: addressId, userId }
      });

      if (!existingAddress) {
        return errorResponse("Shipping address not found");
      }

      await prisma.shippingAddress.delete({
        where: { id: addressId }
      });

      return successResponse("Shipping address deleted successfully");
    } catch (error) {
      console.error("Delete shipping address service error:", error);
      return errorResponse("Failed to delete shipping address");
    }
  }

  async createOrder(userId: string, orderData: any) {
    try {
      // First validate the checkout
      const validation = await this.validateCheckoutAccess(
        userId,
        orderData.orderType,
        orderData.productId,
        orderData.bidId
      );

      if (!validation.status) {
        return validation;
      }

      // Use the validated context for order creation
      const context = validation.data?.context;
      if (!context) {
        return errorResponse("Invalid checkout context", { type: 'validation' });
      }

      // Handle shipping address
      let shippingAddressId = orderData.shippingAddressId;
      if (!shippingAddressId && orderData.shippingAddress) {
        const addressResult = await this.createShippingAddress(userId, orderData.shippingAddress);
        if (!addressResult.status) {
          return addressResult;
        }
        shippingAddressId = addressResult.data?.id ?? '';
      }

      if (!shippingAddressId) {
        return errorResponse("Shipping address is required", { type: 'validation' });
      }

      // Calculate order totals based on checkout type
      let subtotal = 0;
      let orderItems: any[] = [];

      switch (context.type) {
        case 'cart':
          subtotal = context.items.reduce((sum, item) => sum + (Number(item.price) * item.quantity), 0);
          orderItems = context.items.map(item => ({
            productId: item.productId,
            quantity: item.quantity,
            price: Number(item.price),
            currency: orderData.currency || 'USD'
          }));
          break;

        case 'buy-now':
          subtotal = Number(context.product.priceUSD);
          if (orderData.currency === 'IDR') {
            const currencyService = (await import('./currency.service')).default;
            subtotal = await currencyService.convertCurrency(subtotal, 'USD', 'IDR');
          }
          orderItems = [{
            productId: context.productId,
            quantity: context.quantity,
            price: subtotal,
            currency: orderData.currency || 'USD'
          }];
          break;

        case 'bidding':
          subtotal = Number(context.winningBid.amount);
          if (orderData.currency === 'IDR') {
            const currencyService = (await import('./currency.service')).default;
            subtotal = await currencyService.convertCurrency(subtotal, 'USD', 'IDR');
          }
          orderItems = [{
            productId: context.productId,
            quantity: 1,
            price: subtotal,
            currency: orderData.currency || 'USD',
            bidId: context.bidId
          }];
          break;

        default:
          return errorResponse("Invalid checkout type", { type: 'validation' });
      }

      // Calculate shipping and tax
      const shippingCost = orderData.currency === 'IDR' ? 310000 : 20.00;
      const tax = subtotal * 0.1;
      const total = subtotal + shippingCost + tax;

      // Create the order
      const order = await prisma.order.create({
        data: {
          userId,
          orderNumber: this.generateOrderNumber(),
          status: 'pending_payment',
          paymentStatus: 'pending',
          paymentMethod: typeof orderData.paymentMethod === 'object'
            ? orderData.paymentMethod?.type || 'xendit_invoice'
            : orderData.paymentMethod || 'xendit_invoice',
          currency: orderData.currency || 'USD',
          subtotal,
          shippingCost,
          tax,
          total,
          shippingAddressId,
          notes: orderData.notes,
          ...(context.type === 'bidding' && {
            bidId: context.bidId
          }),
          items: {
            create: orderItems
          }
        },
        include: {
          items: {
            include: {
              product: {
                include: { images: true }
              },
              bid: true
            }
          },
          shippingAddress: true
        }
      });

      return successResponse("Order created successfully", order);
    } catch (error) {
      return errorResponse("Failed to create order", {
        type: 'unknown',
        details: process.env.NODE_ENV === 'development' ? error : undefined
      });
    }
  }

  // Create order from cart
  async createOrderFromCart(userId: string, orderData: any) {
    try {
      // Get user's cart
      const cart = await prisma.cart.findUnique({
        where: { userId },
        include: {
          items: {
            include: {
              product: {
                include: { images: true }
              }
            }
          }
        }
      });

      if (!cart || cart.items.length === 0) {
        return errorResponse("Cart is empty");
      }

      // Validate all products are available
      for (const item of cart.items) {
        if (item.product.status !== 'active') {
          return errorResponse(`Product "${item.product.itemName}" is no longer available`);
        }
        if (item.product.sellType !== 'buy-now') {
          return errorResponse(`Product "${item.product.itemName}" is only available through auction`);
        }
      }

      // Handle shipping address
      let shippingAddressId = orderData.shippingAddressId;
      if (!shippingAddressId && orderData.shippingAddress) {
        const addressResult = await this.createShippingAddress(userId, orderData.shippingAddress);
        if (!addressResult.status) {
          return addressResult;
        }
        shippingAddressId = addressResult.data?.id ?? '';
      }

      // Calculate totals
      const subtotal = cart.items.reduce((sum, item) => sum + (Number(item.price) * item.quantity), 0);

      // Calculate dynamic shipping cost
      let shippingCost = 0;
      if (shippingAddressId) {
        const shippingResult = await shippingService.getShippingOptions(shippingAddressId, cart.items);
        if (shippingResult.status && shippingResult.data?.rates?.length > 0) {
          // Use the cheapest shipping option by default
          shippingCost = Math.min(...shippingResult.data.rates.map((rate: any) => rate.cost));
        } else {
          // Fallback to fixed cost if shipping calculation fails
          shippingCost = 15.00;
        }
      } else {
        shippingCost = 15.00; // Default shipping cost
      }

      const tax = subtotal * 0.1; // 10% tax
      const total = subtotal + shippingCost + tax;

      // Create order
      const order = await prisma.order.create({
        data: {
          userId,
          orderNumber: this.generateOrderNumber(),
          status: 'pending',
          paymentStatus: 'pending',
          paymentMethod: typeof orderData.paymentMethod === 'object'
            ? orderData.paymentMethod?.type || 'xendit_invoice'
            : orderData.paymentMethod || 'xendit_invoice',
          currency: orderData.currency || 'USD',
          subtotal,
          shippingCost,
          tax,
          total,
          shippingAddressId,
          notes: orderData.notes,
          items: {
            create: cart.items.map(item => ({
              productId: item.productId,
              quantity: item.quantity,
              price: item.price,
              currency: orderData.currency || 'USD'
            }))
          }
        },
        include: {
          items: {
            include: {
              product: {
                include: { images: true }
              },
              bid: true // Include bid information for auction orders
            }
          },
          shippingAddress: true
        }
      });

      // Clear cart after successful order creation
      await prisma.cartItem.deleteMany({
        where: { cartId: cart.id }
      });

      const response = {
        ...order,
        subtotal: Number(order.subtotal),
        shippingCost: Number(order.shippingCost),
        tax: Number(order.tax),
        total: Number(order.total),
        items: order.items.map(item => ({
          ...item,
          price: Number(item.price),
          product: {
            ...item.product,
            priceUSD: Number(item.product.priceUSD),
            currentBid: item.product.currentBid ? Number(item.product.currentBid) : null,
          }
        }))
      };

      return successResponse("Order created successfully", response);
    } catch (error) {
      console.error("Create order from cart service error:", error);
      return errorResponse("Failed to create order");
    }
  }

  // Create buy now order (single product)
  async createBuyNowOrder(userId: string, orderData: any) {
    try {
      const { products: productIds, quantity, paymentMethod } = orderData;

      const products = await prisma.product.findMany({
        where: { id: { in: productIds } },
      });

      if (!products || products.length === 0) {
        return errorResponse("Products not found");
      }

      // For single buy now, just use the first product
      const product = products[0];

      if (!product) {
        return errorResponse("Product not found");
      }

      // Check if all products are active
      const inactiveProducts = products.filter(item => item.status !== 'active');
      if (inactiveProducts.length > 0) {
        return errorResponse("Some products are not available for purchase");
      }

      // Check if all products are buy-now type (unless this is a bidding order)
      if (!orderData.orderType || orderData.orderType !== 'bidding') {
        const auctionProducts = products.filter(item => item.sellType !== 'buy-now');
        if (auctionProducts.length > 0) {
          return errorResponse("Some products are only available through auction");
        }
      }

      // Additional validation for bidding orders
      if (orderData.orderType === 'bidding') {
        // Validate that this is an auction product
        if (product.sellType !== 'auction') {
          return errorResponse("This product is not an auction item");
        }

        // Validate auction has ended
        const now = getJakartaTime();
        if (!product.auctionEndDate || new Date(product.auctionEndDate) > now) {
          return errorResponse("Auction is still ongoing or has no end date");
        }

        // Validate auction is completed
        if (!product.auctionCompleted) {
          return errorResponse("Auction has not been processed yet");
        }

        // Get winning bid and validate user is the winner
        const winningBid = await prisma.bid.findFirst({
          where: { productId: product.id },
          orderBy: { amount: 'desc' },
          include: { bidder: true }
        });

        if (!winningBid) {
          return errorResponse("No bids found for this auction");
        }

        if (winningBid.bidderId !== userId) {
          return errorResponse("You are not the winner of this auction");
        }

        // Check payment deadline
        const auctionEndDate = new Date(product.auctionEndDate);
        const paymentDeadline = new Date(auctionEndDate.getTime() + (3 * 24 * 60 * 60 * 1000));

        if (now > paymentDeadline) {
          return errorResponse("Payment deadline has expired");
        }

        // Check if order already exists for this auction
        const existingOrder = await prisma.order.findFirst({
          where: {
            userId,
            items: {
              some: {
                productId: product.id,
                bidId: winningBid.id
              }
            }
          }
        });

        if (existingOrder) {
          return errorResponse("Order already exists for this auction", {
            existingOrder: {
              id: existingOrder.id,
              orderNumber: existingOrder.orderNumber
            }
          });
        }

        // Set the winning bid data for order creation
        orderData.winningBid = Number(winningBid.amount);
        orderData.bidId = winningBid.id;
      }

      // Handle shipping address
      let shippingAddressId = orderData.shippingAddressId;
      if (!shippingAddressId && orderData.shippingAddress) {
        const addressResult = await this.createShippingAddress(userId, orderData.shippingAddress);
        if (!addressResult.status) {
          return addressResult;
        }
        shippingAddressId = addressResult.data?.id ?? '';
      }

      // Calculate subtotal - use winning bid amount for bidding orders
      let subtotal = 0;
      if (orderData.orderType === 'bidding' && orderData.winningBid) {
        // Winning bid is stored in USD, convert to order currency if needed
        const winningBidUSD = Number(orderData.winningBid);
        if (orderData.currency === 'IDR') {
          // Convert USD to IDR
          const currencyService = (await import('./currency.service')).default;
          subtotal = await currencyService.convertCurrency(winningBidUSD, 'USD', 'IDR');
        } else {
          subtotal = winningBidUSD;
        }
      } else {
        // Regular products - convert from USD to order currency if needed
        const productTotalUSD = products.reduce((total, item) => {
          return total + Number(item.priceUSD ?? 0)
        }, 0);

        if (orderData.currency === 'IDR') {
          const currencyService = (await import('./currency.service')).default;
          subtotal = await currencyService.convertCurrency(productTotalUSD, 'USD', 'IDR');
        } else {
          subtotal = productTotalUSD;
        }
      }

      // Calculate dynamic shipping cost for buy now orders
      let shippingCost = 0;
      if (shippingAddressId) {
        const cartItems = productIds.map((id: string) => ({
          product: products.find(p => p.id === id),
          quantity: quantity || 1
        }));

        const shippingResult = await shippingService.getShippingOptions(shippingAddressId, cartItems);
        if (shippingResult.status && shippingResult.data?.rates?.length > 0) {
          // Use the cheapest shipping option by default
          shippingCost = Math.min(...shippingResult.data.rates.map((rate: any) => rate.cost));
        } else {
          // Fallback to fixed cost if shipping calculation fails
          shippingCost = orderData.currency === 'IDR' ? 310000 : 20.00; // 20 USD = ~310,000 IDR
        }
      } else {
        shippingCost = orderData.currency === 'IDR' ? 310000 : 20.00; // Default shipping cost
      }

      const tax = subtotal * 0.1;
      const total = subtotal + shippingCost + tax;

      // Create order
      const order = await prisma.order.create({
        data: {
          userId,
          orderNumber: this.generateOrderNumber(),
          status: 'pending_payment',
          paymentStatus: 'pending',
          paymentMethod: typeof paymentMethod === 'object'
            ? (paymentMethod?.eWalletType ?? paymentMethod?.type ?? 'xendit_invoice')
            : paymentMethod || 'xendit_invoice',
          currency: orderData.currency || 'USD',
          subtotal,
          shippingCost,
          tax,
          total,
          shippingAddressId,
          notes: orderData.notes,
          // Add bidId for bidding orders
          ...(orderData.orderType === 'bidding' && orderData.bidId && {
            bidId: orderData.bidId
          }),
          items: {
            create: productIds.map((id: string) => {
              const product = products.find(p => p.id === id);
              let itemPrice = Number(product?.priceUSD ?? 0);

              // For bidding orders, use the converted subtotal as the item price
              if (orderData.orderType === 'bidding' && orderData.winningBid) {
                itemPrice = subtotal; // This is already converted to the correct currency
              } else if (orderData.currency === 'IDR') {
                // Convert regular product price to IDR if needed
                itemPrice = subtotal / productIds.length; // Distribute total among items
              }

              return {
                productId: id,
                quantity: quantity || 1,
                price: itemPrice,
                currency: orderData.currency || 'USD',
                // Add bidding information if applicable
                ...(orderData.orderType === 'bidding' && orderData.bidId && {
                  bidId: orderData.bidId
                })
              };
            })
          }
        },
        include: {
          items: {
            include: {
              product: {
                include: { images: true }
              },
              bid: true // Include bid information for auction orders
            }
          },
          shippingAddress: true
        }
      });

      // Get user data for payment
      const user = await prisma.user.findUnique({
        where: { id: userId }
      });

      if (!user) {
        return errorResponse("User not found");
      }

      // Create payment record and Xendit payment
      const paymentService = (await import('./payment.service')).default;
      let paymentResult = null;

      console.log("Creating payment for order:", order.id, "with method:", orderData.paymentMethod.type);

      // Create payment based on payment method
      if (orderData.paymentMethod.type === 'xendit_invoice' || orderData.paymentMethod.type === 'invoice') {
        console.log("Creating Xendit invoice for order:", order.id);
        paymentResult = await paymentService.createInvoice({
          orderId: order.id,
          currency: orderData.currency || 'USD',
          description: `Payment for Order ${order.orderNumber}`,
          customerEmail: user.email || '<EMAIL>',
          customerName: `${user.firstName} ${user.lastName}`
        });
      } else if (orderData.paymentMethod.type === 'ewallet') {
        console.log("Creating eWallet payment for order:", order.id, "with type:", orderData.paymentMethod.channel);
        paymentResult = await paymentService.createEWalletCharge({
          orderId: order.id,
          currency: orderData.currency || 'USD',
          ewalletType: orderData.paymentMethod.ewalletType || 'OVO',
          customerPhone: user.phoneNumber || '************',
          customerName: `${user.firstName} ${user.lastName}`
        });
      } else if (orderData.paymentMethod.type === 'virtual_account') {
        console.log("Creating virtual account payment for order:", order.id, "with bank code:", orderData.paymentMethod.channel);
        paymentResult = await paymentService.createVirtualAccount({
          orderId: order.id,
          currency: orderData.currency || 'IDR',
          bankCode: orderData.paymentMethod.ewalletType || 'BCA',
          customerName: `${user.firstName} ${user.lastName}`
        });
      } else if (orderData.paymentMethod.type === 'retail_outlet') {
        console.log("Creating retail outlet payment for order:", order.id, "with outlet name:", orderData.paymentMethod.channel);
        paymentResult = await paymentService.createRetailOutlet({
          orderId: order.id,
          currency: orderData.currency || 'IDR',
          retailOutletName: orderData.paymentMethod.ewalletType || 'INDOMARET',
          customerName: `${user.firstName} ${user.lastName}`
        });
      } else if (orderData.paymentMethod.type === 'qr_code') {
        console.log("Creating QR code payment for order:", order.id, "with QR code type:", orderData.paymentMethod.channel);
        paymentResult = await paymentService.createQRCode({
          orderId: order.id,
          currency: orderData.currency || 'IDR',
          qrCodeType: orderData.paymentMethod.ewalletType || 'QRIS',
          customerName: `${user.firstName} ${user.lastName}`
        });
      }

      // Clear cart if this is a buy now order
      await prisma.cartItem.deleteMany({
        where: { cart: { userId } }
      });

      // Get updated order with payment
      const updatedOrder = await prisma.order.findUnique({
        where: { id: order.id },
        include: {
          items: {
            include: {
              product: {
                include: { images: true }
              },
              bid: true // Include bid information for auction orders
            }
          },
          shippingAddress: true,
          payment: true
        }
      });

      const response = {
        ...updatedOrder,
        subtotal: Number(updatedOrder!.subtotal),
        shippingCost: Number(updatedOrder!.shippingCost),
        tax: Number(updatedOrder!.tax),
        total: Number(updatedOrder!.total),
        items: updatedOrder!.items.map(item => ({
          ...item,
          price: Number(item.price),
          product: products.map(prod => ({
            ...prod,
            priceUSD: Number(prod.priceUSD),
            currentBid: prod.currentBid ? Number(prod.currentBid) : null,
          }))
        })),
        paymentUrl: (paymentResult?.data as any)?.invoiceUrl || (paymentResult?.data as any)?.actions?.mobile_deeplink_checkout_url
      };

      return successResponse("Order created successfully", response);
    } catch (error) {
      console.error("Create buy now order service error:", error);
      return errorResponse("Failed to create order");
    }
  }

  // Get user's orders
  async getOrders(userId: string, query: any) {
    try {
      const { page, limit, status, paymentStatus, sortBy, sortOrder } = query;
      const skip = (page - 1) * limit;

      const where: any = { userId };
      if (status) where.status = status;
      if (paymentStatus) where.paymentStatus = paymentStatus;

      const orders = await prisma.order.findMany({
        where,
        include: {
          items: {
            include: {
              product: {
                include: { images: true }
              },
              bid: true // Include bid information for auction orders
            }
          },
          shippingAddress: true
        },
        orderBy: { [sortBy]: sortOrder },
        skip,
        take: limit
      });

      const total = await prisma.order.count({ where });

      const response = {
        orders: orders.map(order => ({
          ...order,
          subtotal: Number(order.subtotal),
          shippingCost: Number(order.shippingCost),
          tax: Number(order.tax),
          total: Number(order.total),
          items: order.items.map(item => ({
            ...item,
            price: Number(item.price),
            product: {
              ...item.product,
              priceUSD: Number(item.product.priceUSD),
              currentBid: item.product.currentBid ? Number(item.product.currentBid) : null,
            }
          }))
        })),
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit)
        }
      };

      return successResponse("Orders retrieved successfully", response);
    } catch (error) {
      console.error("Get orders service error:", error);
      return errorResponse("Failed to retrieve orders");
    }
  }

  // Get single order
  async getOrder(userId: string, orderId: string) {
    try {
      const order = await prisma.order.findFirst({
        where: { id: orderId, userId },
        include: {
          items: {
            include: {
              product: {
                include: { images: true }
              },
              bid: true // Include bid information for auction orders
            }
          },
          shippingAddress: true
        }
      });

      if (!order) {
        return errorResponse("Order not found");
      }

      const response = {
        ...order,
        subtotal: Number(order.subtotal),
        shippingCost: Number(order.shippingCost),
        tax: Number(order.tax),
        total: Number(order.total),
        items: order.items.map(item => ({
          ...item,
          price: Number(item.price),
          product: {
            ...item.product,
            priceUSD: Number(item.product.priceUSD),
            currentBid: item.product.currentBid ? Number(item.product.currentBid) : null,
          }
        }))
      };

      return successResponse("Order retrieved successfully", response);
    } catch (error) {
      console.error("Get order service error:", error);
      return errorResponse("Failed to retrieve order");
    }
  }

  // Update order status (admin function)
  async updateOrderStatus(orderId: string, statusData: any) {
    try {
      const order = await prisma.order.update({
        where: { id: orderId },
        data: statusData,
        include: {
          items: {
            include: {
              product: {
                include: { images: true }
              },
              bid: true // Include bid information for auction orders
            }
          },
          shippingAddress: true
        }
      });

      const response = {
        ...order,
        subtotal: Number(order.subtotal),
        shippingCost: Number(order.shippingCost),
        tax: Number(order.tax),
        total: Number(order.total),
        items: order.items.map(item => ({
          ...item,
          price: Number(item.price),
          product: {
            ...item.product,
            priceUSD: Number(item.product.priceUSD),
            currentBid: item.product.currentBid ? Number(item.product.currentBid) : null,
          }
        }))
      };

      return successResponse("Order status updated successfully", response);
    } catch (error) {
      console.error("Update order status service error:", error);
      return errorResponse("Failed to update order status");
    }
  }
}

const checkoutService = new CheckoutService();
export default checkoutService;
