import { prisma } from "../db";
import { successResponse, errorResponse } from "../utils/response.util";
import { getJakartaTime } from "../utils/timezone.util";

class CheckoutService {
  // Generate unique order number
  private generateOrderNumber(): string {
    const timestamp = Date.now().toString();
    const random = Math.random().toString(36).substring(2, 8).toUpperCase();
    return `ORD-${timestamp}-${random}`;
  }

  async validateCheckoutAccess(userId: string, checkoutType: string, productId?: string, bidId?: string) {
    try {
      // Input validation
      if (!userId) {
        return errorResponse("User ID is required", { type: 'validation' });
      }

      if (!['cart', 'buy-now', 'bidding'].includes(checkoutType)) {
        return errorResponse("Invalid checkout type. Must be 'cart', 'buy-now', or 'bidding'", { type: 'validation' });
      }

      switch (checkoutType) {
        case 'cart':
          return await this.validateCartCheckout(userId);

        case 'buy-now':
          if (!productId) {
            return errorResponse("Product ID is required for buy-now checkout", { type: 'validation' });
          }
          return await this.validateBuyNowCheckout(userId, productId);

        case 'bidding':
          if (!productId) {
            return errorResponse("Product ID is required for bidding checkout", { type: 'validation' });
          }
          return await this.validateBiddingCheckout(userId, productId, bidId);

        default:
          return errorResponse("Invalid checkout type", { type: 'validation' });
      }
    } catch (error) {
      console.error("Validate checkout access error:", error);
      return errorResponse("Failed to validate checkout access", {
        type: 'unknown',
        details: process.env.NODE_ENV === 'development' ? error : undefined
      });
    }
  }

  private async validateCartCheckout(userId: string) {
    try {
      const cart = await prisma.cart.findUnique({
        where: { userId },
        include: {
          items: {
            include: {
              product: {
                include: {
                  images: true
                }
              }
            }
          }
        }
      });

      if (!cart || cart.items.length === 0) {
        return errorResponse("Your cart is empty. Please add items before checkout.", {
          type: 'validation',
          checkoutType: 'cart'
        });
      }

      // Validate each cart item
      const invalidItems = [];
      const validItems = [];

      for (const item of cart.items) {
        if (item.product.status !== 'active') {
          invalidItems.push({
            productId: item.product.id,
            itemName: item.product.itemName,
            reason: 'Product is no longer available'
          });
        } else if (item.product.sellType !== 'buy-now') {
          invalidItems.push({
            productId: item.product.id,
            itemName: item.product.itemName,
            reason: 'Product is only available through auction'
          });
        } else {
          validItems.push(item);
        }
      }

      if (invalidItems.length > 0) {
        return errorResponse("Some items in your cart are no longer available for purchase", {
          type: 'inventory',
          checkoutType: 'cart',
          invalidItems,
          validItemsCount: validItems.length
        });
      }

      // Calculate totals
      const totalItems = cart.items.reduce((sum, item) => sum + item.quantity, 0);
      const totalPrice = cart.items.reduce((sum, item) => sum + (Number(item.price) * item.quantity), 0);

      // Build checkout context
      const checkoutContext = {
        type: 'cart' as const,
        items: cart.items.map(item => ({
          id: item.id,
          productId: item.productId,
          quantity: item.quantity,
          price: item.price,
          product: {
            id: item.product.id,
            itemName: item.product.itemName,
            slug: item.product.slug || '',
            priceUSD: Number(item.product.priceUSD),
            images: item.product.images.map(img => ({
              id: img.id,
              imageUrl: img.imageUrl,
              isMain: img.isMain
            })),
            sellType: item.product.sellType as 'buy-now',
            status: item.product.status
          }
        })),
        totalItems,
        totalPrice
      };

      return successResponse("Cart checkout validated successfully", {
        checkoutType: 'cart',
        context: checkoutContext
      });
    } catch (error) {
      console.error("Cart checkout validation error:", error);
      return errorResponse("Failed to validate cart checkout", {
        type: 'unknown',
        checkoutType: 'cart'
      });
    }
  }

  private async validateBuyNowCheckout(_userId: string, productId: string) {
    try {
      const product = await prisma.product.findUnique({
        where: { id: productId },
        include: {
          images: true,
          seller: {
            select: {
              id: true,
              firstName: true,
              lastName: true
            }
          }
        }
      });

      if (!product) {
        return errorResponse("Product not found. It may have been removed or is no longer available.", {
          type: 'validation',
          checkoutType: 'buy-now',
          productId
        });
      }

      if (product.status !== 'active') {
        return errorResponse("This product is no longer available for purchase.", {
          type: 'inventory',
          checkoutType: 'buy-now',
          productId,
          productStatus: product.status
        });
      }

      if (product.sellType !== 'buy-now') {
        return errorResponse("This product is only available through auction bidding.", {
          type: 'validation',
          checkoutType: 'buy-now',
          productId,
          actualSellType: product.sellType
        });
      }

      // Build checkout context
      const checkoutContext = {
        type: 'buy-now' as const,
        productId: product.id,
        product: {
          id: product.id,
          itemName: product.itemName,
          slug: product.slug || '',
          priceUSD: Number(product.priceUSD),
          images: product.images.map(img => ({
            id: img.id,
            imageUrl: img.imageUrl,
            isMain: img.isMain
          })),
          sellType: product.sellType as 'buy-now',
          status: product.status
        },
        quantity: 1
      };

      return successResponse("Buy-now checkout validated successfully", {
        checkoutType: 'buy-now',
        context: checkoutContext
      });
    } catch (error) {
      console.error("Buy-now checkout validation error:", error);
      return errorResponse("Failed to validate buy-now checkout", {
        type: 'unknown',
        checkoutType: 'buy-now',
        productId
      });
    }
  }

  private async validateBiddingCheckout(userId: string, productId: string, _bidId?: string) {
    try {
      const now = getJakartaTime();

      // Get product details with winner information
      const product = await prisma.product.findUnique({
        where: { id: productId },
        include: {
          images: true,
          winner: {
            select: {
              id: true,
              firstName: true,
              lastName: true
            }
          },
          winnerBid: true,
          bids: {
            orderBy: { amount: 'desc' },
            take: 1,
            include: {
              bidder: {
                select: {
                  id: true,
                  firstName: true,
                  lastName: true
                }
              }
            }
          }
        }
      });

      if (!product) {
        return errorResponse("Auction product not found. It may have been removed.", {
          type: 'validation',
          checkoutType: 'bidding',
          productId
        });
      }

      if (product.sellType !== 'auction') {
        return errorResponse("This product is not an auction item. Please use buy-now checkout instead.", {
          type: 'validation',
          checkoutType: 'bidding',
          productId,
          actualSellType: product.sellType
        });
      }

      // Check if auction has ended
      if (!product.auctionEndDate || new Date(product.auctionEndDate) > now) {
        return errorResponse("This auction is still ongoing. Please wait for the auction to end.", {
          type: 'validation',
          checkoutType: 'bidding',
          productId,
          auctionEndDate: product.auctionEndDate,
          isOngoing: true
        });
      }

      // Check if auction is completed
      if (!product.auctionCompleted) {
        return errorResponse("This auction has ended but is still being processed. Please check back in a few minutes.", {
          type: 'validation',
          checkoutType: 'bidding',
          productId,
          auctionEndDate: product.auctionEndDate,
          isProcessing: true
        });
      }

      // Check if there's a winner set in the product
      if (!product.winnerId || !product.winnerBidId) {
        return errorResponse("No winner has been determined for this auction yet.", {
          type: 'validation',
          checkoutType: 'bidding',
          productId,
          noWinner: true
        });
      }

      // Verify user is the winner using the winnerId field
      if (product.winnerId !== userId) {
        return errorResponse("You are not the winner of this auction. Only the winning bidder can checkout.", {
          type: 'validation',
          checkoutType: 'bidding',
          productId,
          winnerId: product.winnerId,
          notWinner: true
        });
      }

      // Get the winning bid (fallback to bids if winnerBid relation is not loaded)
      const winningBid = product.winnerBid || product.bids[0];
      if (!winningBid) {
        return errorResponse("No winning bid found for this auction.", {
          type: 'validation',
          checkoutType: 'bidding',
          productId,
          noWinningBid: true
        });
      }

      // Calculate payment deadline (3 days after auction end)
      const auctionEndDate = new Date(product.auctionEndDate);
      const paymentDeadline = new Date(auctionEndDate.getTime() + (3 * 24 * 60 * 60 * 1000));

      // Check if payment deadline has expired
      if (now > paymentDeadline) {
        return errorResponse("Payment deadline has expired. You can no longer checkout for this auction.", {
          type: 'validation',
          checkoutType: 'bidding',
          productId,
          expired: true,
          paymentDeadline: paymentDeadline.toISOString(),
          auctionEndDate: auctionEndDate.toISOString()
        });
      }

      // Check if user already has an order for this auction
      const existingOrder = await prisma.order.findFirst({
        where: {
          userId,
          OR: [
            { bidId: winningBid.id },
            {
              items: {
                some: {
                  productId,
                  bidId: winningBid.id
                }
              }
            }
          ]
        },
        include: {
          items: true
        }
      });

      if (existingOrder) {
        return errorResponse("You already have an order for this auction. Please complete the payment for your existing order.", {
          type: 'validation',
          checkoutType: 'bidding',
          productId,
          existingOrder: {
            id: existingOrder.id,
            orderNumber: existingOrder.orderNumber,
            status: existingOrder.status,
            paymentStatus: existingOrder.paymentStatus,
            total: Number(existingOrder.total),
            currency: existingOrder.currency
          }
        });
      }

      // Calculate time remaining for payment
      const timeRemainingMs = paymentDeadline.getTime() - now.getTime();
      const hoursRemaining = Math.max(0, Math.floor(timeRemainingMs / (1000 * 60 * 60)));

      // Build checkout context
      const checkoutContext = {
        type: 'bidding' as const,
        productId: product.id,
        bidId: winningBid.id,
        product: {
          id: product.id,
          itemName: product.itemName,
          slug: product.slug || '',
          priceUSD: Number(product.priceUSD),
          images: product.images.map(img => ({
            id: img.id,
            imageUrl: img.imageUrl,
            isMain: img.isMain
          })),
          sellType: product.sellType as 'auction',
          status: product.status,
          auctionEndDate: product.auctionEndDate,
          auctionCompleted: product.auctionCompleted,
          winnerId: product.winnerId,
          winnerBidId: product.winnerBidId
        },
        winningBid: {
          id: winningBid.id,
          amount: Number(winningBid.amount),
          bidderId: winningBid.bidderId,
          bidder: (winningBid as any).bidder || product.winner
        },
        paymentDeadline: paymentDeadline.toISOString(),
        hoursRemaining,
        auctionEndDate: auctionEndDate.toISOString()
      };

      return successResponse("Bidding checkout validated successfully", {
        checkoutType: 'bidding',
        context: checkoutContext
      });
    } catch (error) {
      console.error("Bidding checkout validation error:", error);
      return errorResponse("Failed to validate bidding checkout", {
        type: 'unknown',
        checkoutType: 'bidding',
        productId
      });
    }
  }

  // Create shipping address
  async createShippingAddress(userId: string, addressData: any) {
    try {
      // If this is set as default, unset other default addresses
      if (addressData.isDefault) {
        await prisma.shippingAddress.updateMany({
          where: { userId, isDefault: true },
          data: { isDefault: false }
        });
      }

      const address = await prisma.shippingAddress.create({
        data: {
          ...addressData,
          userId
        }
      });

      return successResponse("Shipping address created successfully", address);
    } catch (error) {
      console.error("Create shipping address service error:", error);
      return errorResponse("Failed to create shipping address");
    }
  }

  // Get user's shipping addresses
  async getShippingAddresses(userId: string) {
    try {
      const addresses = await prisma.shippingAddress.findMany({
        where: { userId },
        orderBy: [
          { isDefault: 'desc' },
          { createdAt: 'desc' }
        ]
      });

      return successResponse("Shipping addresses retrieved successfully", addresses);
    } catch (error) {
      console.error("Get shipping addresses service error:", error);
      return errorResponse("Failed to retrieve shipping addresses");
    }
  }

  // Update shipping address
  async updateShippingAddress(userId: string, addressId: string, addressData: any) {
    try {
      // Verify address belongs to user
      const existingAddress = await prisma.shippingAddress.findFirst({
        where: { id: addressId, userId }
      });

      if (!existingAddress) {
        return errorResponse("Shipping address not found");
      }

      // If this is set as default, unset other default addresses
      if (addressData.isDefault) {
        await prisma.shippingAddress.updateMany({
          where: { userId, isDefault: true, id: { not: addressId } },
          data: { isDefault: false }
        });
      }

      const address = await prisma.shippingAddress.update({
        where: { id: addressId },
        data: addressData
      });

      return successResponse("Shipping address updated successfully", address);
    } catch (error) {
      console.error("Update shipping address service error:", error);
      return errorResponse("Failed to update shipping address");
    }
  }

  // Delete shipping address
  async deleteShippingAddress(userId: string, addressId: string) {
    try {
      // Verify address belongs to user
      const existingAddress = await prisma.shippingAddress.findFirst({
        where: { id: addressId, userId }
      });

      if (!existingAddress) {
        return errorResponse("Shipping address not found");
      }

      await prisma.shippingAddress.delete({
        where: { id: addressId }
      });

      return successResponse("Shipping address deleted successfully");
    } catch (error) {
      console.error("Delete shipping address service error:", error);
      return errorResponse("Failed to delete shipping address");
    }
  }

  async createOrder(userId: string, orderData: any) {
    try {
      // First validate the checkout
      const validation = await this.validateCheckoutAccess(
        userId,
        orderData.orderType,
        orderData.productId,
        orderData.bidId
      );

      if (!validation.status) {
        return validation;
      }

      // Use the validated context for order creation
      const context = validation.data?.context;
      if (!context) {
        return errorResponse("Invalid checkout context", { type: 'validation' });
      }

      // Handle shipping address
      let shippingAddressId = orderData.shippingAddressId;
      if (!shippingAddressId && orderData.shippingAddress) {
        const addressResult = await this.createShippingAddress(userId, orderData.shippingAddress);
        if (!addressResult.status) {
          return addressResult;
        }
        shippingAddressId = addressResult.data?.id ?? '';
      }

      if (!shippingAddressId) {
        return errorResponse("Shipping address is required", { type: 'validation' });
      }

      // Calculate order totals based on checkout type
      let subtotal = 0;
      let orderItems: any[] = [];

      switch (context.type) {
        case 'cart':
          subtotal = context.items.reduce((sum, item) => sum + (Number(item.price) * item.quantity), 0);
          orderItems = context.items.map(item => ({
            productId: item.productId,
            quantity: item.quantity,
            price: Number(item.price),
            currency: orderData.currency || 'USD'
          }));
          break;

        case 'buy-now':
          subtotal = Number(context.product.priceUSD);
          if (orderData.currency === 'IDR') {
            const currencyService = (await import('./currency.service')).default;
            subtotal = await currencyService.convertCurrency(subtotal, 'USD', 'IDR');
          }
          orderItems = [{
            productId: context.productId,
            quantity: context.quantity,
            price: subtotal,
            currency: orderData.currency || 'USD'
          }];
          break;

        case 'bidding':
          subtotal = Number(context.winningBid.amount);
          if (orderData.currency === 'IDR') {
            const currencyService = (await import('./currency.service')).default;
            subtotal = await currencyService.convertCurrency(subtotal, 'USD', 'IDR');
          }
          orderItems = [{
            productId: context.productId,
            quantity: 1,
            price: subtotal,
            currency: orderData.currency || 'USD',
            bidId: context.bidId
          }];
          break;

        default:
          return errorResponse("Invalid checkout type", { type: 'validation' });
      }

      // Calculate shipping and tax
      const shippingCost = orderData.currency === 'IDR' ? 310000 : 20.00;
      const tax = subtotal * 0.1;
      const total = subtotal + shippingCost + tax;

      // Create the order
      const order = await prisma.order.create({
        data: {
          userId,
          orderNumber: this.generateOrderNumber(),
          status: 'pending_payment',
          paymentStatus: 'pending',
          paymentMethod: typeof orderData.paymentMethod === 'object'
            ? orderData.paymentMethod?.type || 'xendit_invoice'
            : orderData.paymentMethod || 'xendit_invoice',
          currency: orderData.currency || 'USD',
          subtotal,
          shippingCost,
          tax,
          total,
          shippingAddressId,
          notes: orderData.notes,
          ...(context.type === 'bidding' && {
            bidId: context.bidId
          }),
          items: {
            create: orderItems
          }
        },
        include: {
          items: {
            include: {
              product: {
                include: { images: true }
              },
              bid: true
            }
          },
          shippingAddress: true
        }
      });

      return successResponse("Order created successfully", order);
    } catch (error) {
      return errorResponse("Failed to create order", {
        type: 'unknown',
        details: process.env.NODE_ENV === 'development' ? error : undefined
      });
    }
  }

  // Optimized create order from cart
  async createOrderFromCart(userId: string, orderData: any) {
    const startTime = Date.now();
    console.log(`[CHECKOUT] Starting cart checkout for user ${userId}`);

    try {
      // Single optimized query to get cart with all needed data
      const cart = await prisma.cart.findUnique({
        where: { userId },
        include: {
          items: {
            include: {
              product: {
                select: {
                  id: true,
                  itemName: true,
                  status: true,
                  sellType: true,
                  priceUSD: true,
                  currentBid: true,
                  images: {
                    select: {
                      id: true,
                      imageUrl: true,
                      isMain: true
                    },
                    orderBy: { sortOrder: 'asc' },
                    take: 1 // Only get main image for performance
                  }
                }
              }
            }
          }
        }
      });

      if (!cart || cart.items.length === 0) {
        console.log(`[CHECKOUT] Cart is empty for user ${userId}`);
        return errorResponse("Cart is empty", { type: 'validation' });
      }

      console.log(`[CHECKOUT] Found ${cart.items.length} items in cart`);

      // Batch validate all products
      const invalidItems = cart.items.filter(item =>
        item.product.status !== 'active' || item.product.sellType !== 'buy-now'
      );

      if (invalidItems.length > 0) {
        const invalidItem = invalidItems[0];
        const reason = invalidItem.product.status !== 'active'
          ? 'no longer available'
          : 'only available through auction';
        return errorResponse(`Product "${invalidItem.product.itemName}" is ${reason}`, { type: 'validation' });
      }

      // Handle shipping address efficiently
      let shippingAddressId = orderData.shippingAddressId;
      if (!shippingAddressId && orderData.shippingAddress) {
        const addressResult = await this.createShippingAddress(userId, orderData.shippingAddress);
        if (!addressResult.status) {
          return addressResult;
        }
        shippingAddressId = addressResult.data?.id ?? '';
      }

      // Calculate totals efficiently
      const subtotal = cart.items.reduce((sum, item) => sum + (Number(item.price) * item.quantity), 0);

      // Simplified shipping calculation for better performance
      const shippingCost = orderData.currency === 'IDR' ? 310000 : 20.00;
      const tax = subtotal * 0.1;
      const total = subtotal + shippingCost + tax;

      console.log(`[CHECKOUT] Calculated totals - Subtotal: ${subtotal}, Shipping: ${shippingCost}, Tax: ${tax}, Total: ${total}`);

      // Use transaction for atomicity and better performance
      const result = await prisma.$transaction(async (tx) => {
        // Create order with optimized includes
        const order = await tx.order.create({
          data: {
            userId,
            orderNumber: this.generateOrderNumber(),
            status: 'pending_payment',
            paymentStatus: 'pending',
            paymentMethod: typeof orderData.paymentMethod === 'object'
              ? orderData.paymentMethod?.type || 'xendit_invoice'
              : orderData.paymentMethod || 'xendit_invoice',
            currency: orderData.currency || 'USD',
            subtotal,
            shippingCost,
            tax,
            total,
            shippingAddressId,
            notes: orderData.notes,
            items: {
              create: cart.items.map(item => ({
                productId: item.productId,
                quantity: item.quantity,
                price: item.price,
                currency: orderData.currency || 'USD'
              }))
            }
          },
          include: {
            items: {
              include: {
                product: {
                  select: {
                    id: true,
                    itemName: true,
                    slug: true,
                    priceUSD: true,
                    currentBid: true,
                    images: {
                      select: {
                        id: true,
                        imageUrl: true,
                        isMain: true
                      },
                      take: 1
                    }
                  }
                }
              }
            },
            shippingAddress: true
          }
        });

        // Clear cart items in same transaction
        await tx.cartItem.deleteMany({
          where: { cartId: cart.id }
        });

        return order;
      });

      const processingTime = Date.now() - startTime;
      console.log(`[CHECKOUT] Cart checkout completed in ${processingTime}ms for order ${result.orderNumber}`);

      // Format response efficiently
      const response = {
        ...result,
        subtotal: Number(result.subtotal),
        shippingCost: Number(result.shippingCost),
        tax: Number(result.tax),
        total: Number(result.total),
        items: result.items.map(item => ({
          ...item,
          price: Number(item.price),
          product: {
            ...item.product,
            priceUSD: Number(item.product.priceUSD),
            currentBid: item.product.currentBid ? Number(item.product.currentBid) : null,
          }
        }))
      };

      return successResponse("Cart order created successfully", response);
    } catch (error) {
      const processingTime = Date.now() - startTime;
      console.error(`[CHECKOUT] Cart checkout failed after ${processingTime}ms:`, error);
      return errorResponse("Failed to create cart order", {
        type: 'unknown',
        details: process.env.NODE_ENV === 'development' ? error : undefined
      });
    }
  }

  // Optimized create buy now order
  async createBuyNowOrder(userId: string, orderData: any) {
    const startTime = Date.now();
    console.log(`[CHECKOUT] Starting buy-now checkout for user ${userId}`);

    try {
      const { products: productIds, quantity = 1, paymentMethod } = orderData;

      if (!productIds || productIds.length === 0) {
        return errorResponse("Product ID is required", { type: 'validation' });
      }

      // Optimized single query with only needed fields
      const products = await prisma.product.findMany({
        where: { id: { in: productIds } },
        select: {
          id: true,
          itemName: true,
          status: true,
          sellType: true,
          priceUSD: true,
          currentBid: true,
          auctionEndDate: true,
          auctionCompleted: true,
          images: {
            select: {
              id: true,
              imageUrl: true,
              isMain: true
            },
            take: 1
          }
        }
      });

      if (!products || products.length === 0) {
        return errorResponse("Product not found", { type: 'validation' });
      }

      const product = products[0];
      console.log(`[CHECKOUT] Processing buy-now for product: ${product.itemName}`);

      // Batch validation for better performance
      const inactiveProducts = products.filter(item => item.status !== 'active');
      if (inactiveProducts.length > 0) {
        return errorResponse("Product is not available for purchase", { type: 'validation' });
      }

      // Check product type unless this is a bidding order
      if (!orderData.orderType || orderData.orderType !== 'bidding') {
        const auctionProducts = products.filter(item => item.sellType !== 'buy-now');
        if (auctionProducts.length > 0) {
          return errorResponse("Product is only available through auction", { type: 'validation' });
        }
      }

      // Optimized bidding validation
      if (orderData.orderType === 'bidding') {
        console.log(`[CHECKOUT] Processing bidding order for auction: ${product.itemName}`);

        // Validate auction product
        if (product.sellType !== 'auction') {
          return errorResponse("This product is not an auction item", { type: 'validation' });
        }

        // Validate auction status
        const now = getJakartaTime();
        if (!product.auctionEndDate || new Date(product.auctionEndDate) > now) {
          return errorResponse("Auction is still ongoing or has no end date", { type: 'validation' });
        }

        if (!product.auctionCompleted) {
          return errorResponse("Auction has not been processed yet", { type: 'validation' });
        }

        // Optimized winning bid query with existence check
        const [winningBid, existingOrder] = await Promise.all([
          prisma.bid.findFirst({
            where: { productId: product.id },
            orderBy: { amount: 'desc' },
            select: {
              id: true,
              amount: true,
              bidderId: true,
              bidder: {
                select: { id: true, email: true }
              }
            }
          }),
          prisma.order.findFirst({
            where: {
              userId,
              items: {
                some: { productId: product.id }
              }
            },
            select: { id: true, orderNumber: true }
          })
        ]);

        if (!winningBid) {
          return errorResponse("No bids found for this auction", { type: 'validation' });
        }

        if (winningBid.bidderId !== userId) {
          return errorResponse("You are not the winner of this auction", { type: 'validation' });
        }

        // Check payment deadline
        const auctionEndDate = new Date(product.auctionEndDate);
        const paymentDeadline = new Date(auctionEndDate.getTime() + (3 * 24 * 60 * 60 * 1000));

        if (now > paymentDeadline) {
          return errorResponse("Payment deadline has expired", { type: 'validation' });
        }

        if (existingOrder) {
          return errorResponse("Order already exists for this auction", {
            type: 'validation',
            existingOrder: {
              id: existingOrder.id,
              orderNumber: existingOrder.orderNumber
            }
          });
        }

        // Set winning bid data
        orderData.winningBid = Number(winningBid.amount);
        orderData.bidId = winningBid.id;
        console.log(`[CHECKOUT] Validated winning bid: ${winningBid.amount}`);
      }

      // Handle shipping address efficiently
      let shippingAddressId = orderData.shippingAddressId;
      if (!shippingAddressId && orderData.shippingAddress) {
        const addressResult = await this.createShippingAddress(userId, orderData.shippingAddress);
        if (!addressResult.status) {
          return addressResult;
        }
        shippingAddressId = addressResult.data?.id ?? '';
      }

      // Optimized subtotal calculation
      let subtotal = 0;
      if (orderData.orderType === 'bidding' && orderData.winningBid) {
        const winningBidUSD = Number(orderData.winningBid);
        if (orderData.currency === 'IDR') {
          const currencyService = (await import('./currency.service')).default;
          subtotal = await currencyService.convertCurrency(winningBidUSD, 'USD', 'IDR');
        } else {
          subtotal = winningBidUSD;
        }
        console.log(`[CHECKOUT] Bidding subtotal calculated: ${subtotal} ${orderData.currency}`);
      } else {
        // Regular buy-now products
        const productTotalUSD = products.reduce((total, item) => total + Number(item.priceUSD ?? 0), 0) * quantity;

        if (orderData.currency === 'IDR') {
          const currencyService = (await import('./currency.service')).default;
          subtotal = await currencyService.convertCurrency(productTotalUSD, 'USD', 'IDR');
        } else {
          subtotal = productTotalUSD;
        }
        console.log(`[CHECKOUT] Buy-now subtotal calculated: ${subtotal} ${orderData.currency}`);
      }

      // Simplified shipping calculation for better performance
      const shippingCost = orderData.currency === 'IDR' ? 310000 : 20.00;
      const tax = subtotal * 0.1;
      const total = subtotal + shippingCost + tax;

      console.log(`[CHECKOUT] Final totals - Subtotal: ${subtotal}, Shipping: ${shippingCost}, Tax: ${tax}, Total: ${total}`);

      // Create order with optimized transaction
      const result = await prisma.$transaction(async (tx) => {
        const order = await tx.order.create({
          data: {
            userId,
            orderNumber: this.generateOrderNumber(),
            status: 'pending_payment',
            paymentStatus: 'pending',
            paymentMethod: typeof paymentMethod === 'object'
              ? (paymentMethod?.eWalletType ?? paymentMethod?.type ?? 'xendit_invoice')
              : paymentMethod || 'xendit_invoice',
            currency: orderData.currency || 'USD',
            subtotal,
            shippingCost,
            tax,
            total,
            shippingAddressId,
            notes: orderData.notes,
            // Add bidId for bidding orders
            ...(orderData.orderType === 'bidding' && orderData.bidId && {
              bidId: orderData.bidId
            }),
            items: {
              create: productIds.map((id: string) => {
                const product = products.find(p => p.id === id);
                let itemPrice = Number(product?.priceUSD ?? 0) * quantity;

                // For bidding orders, use the converted subtotal as the item price
                if (orderData.orderType === 'bidding' && orderData.winningBid) {
                  itemPrice = subtotal;
                } else if (orderData.currency === 'IDR') {
                  itemPrice = subtotal / productIds.length;
                }

                return {
                  productId: id,
                  quantity: quantity,
                  price: itemPrice,
                  currency: orderData.currency || 'USD',
                  ...(orderData.orderType === 'bidding' && orderData.bidId && {
                    bidId: orderData.bidId
                  })
                };
              })
            }
          },
          include: {
            items: {
              include: {
                product: {
                  select: {
                    id: true,
                    itemName: true,
                    slug: true,
                    priceUSD: true,
                    currentBid: true,
                    images: {
                      select: {
                        id: true,
                        imageUrl: true,
                        isMain: true
                      },
                      take: 1
                    }
                  }
                }
              }
            },
            shippingAddress: true
          }
        });

        return order;
      });

      const processingTime = Date.now() - startTime;
      console.log(`[CHECKOUT] Buy-now checkout completed in ${processingTime}ms for order ${result.orderNumber}`);

      // Format response efficiently
      const response = {
        ...result,
        subtotal: Number(result.subtotal),
        shippingCost: Number(result.shippingCost),
        tax: Number(result.tax),
        total: Number(result.total),
        items: result.items.map(item => ({
          ...item,
          price: Number(item.price),
          product: {
            ...item.product,
            priceUSD: Number(item.product.priceUSD),
            currentBid: item.product.currentBid ? Number(item.product.currentBid) : null,
          }
        }))
      };

      return successResponse(
        orderData.orderType === 'bidding' ? "Auction order created successfully" : "Buy-now order created successfully",
        response
      );
    } catch (error) {
      const processingTime = Date.now() - startTime;
      console.error(`[CHECKOUT] Buy-now checkout failed after ${processingTime}ms:`, error);
      return errorResponse("Failed to create order", {
        type: 'unknown',
        details: process.env.NODE_ENV === 'development' ? error : undefined
      });
    }
  }

  // Get user's orders
  async getOrders(userId: string, query: any) {
    try {
      const { page, limit, status, paymentStatus, sortBy, sortOrder } = query;
      const skip = (page - 1) * limit;

      const where: any = { userId };
      if (status) where.status = status;
      if (paymentStatus) where.paymentStatus = paymentStatus;

      const orders = await prisma.order.findMany({
        where,
        include: {
          items: {
            include: {
              product: {
                include: { images: true }
              },
              bid: true // Include bid information for auction orders
            }
          },
          shippingAddress: true
        },
        orderBy: { [sortBy]: sortOrder },
        skip,
        take: limit
      });

      const total = await prisma.order.count({ where });

      const response = {
        orders: orders.map(order => ({
          ...order,
          subtotal: Number(order.subtotal),
          shippingCost: Number(order.shippingCost),
          tax: Number(order.tax),
          total: Number(order.total),
          items: order.items.map(item => ({
            ...item,
            price: Number(item.price),
            product: {
              ...item.product,
              priceUSD: Number(item.product.priceUSD),
              currentBid: item.product.currentBid ? Number(item.product.currentBid) : null,
            }
          }))
        })),
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit)
        }
      };

      return successResponse("Orders retrieved successfully", response);
    } catch (error) {
      console.error("Get orders service error:", error);
      return errorResponse("Failed to retrieve orders");
    }
  }

  // Get single order
  async getOrder(userId: string, orderId: string) {
    try {
      const order = await prisma.order.findFirst({
        where: { id: orderId, userId },
        include: {
          items: {
            include: {
              product: {
                include: { images: true }
              },
              bid: true // Include bid information for auction orders
            }
          },
          shippingAddress: true
        }
      });

      if (!order) {
        return errorResponse("Order not found");
      }

      const response = {
        ...order,
        subtotal: Number(order.subtotal),
        shippingCost: Number(order.shippingCost),
        tax: Number(order.tax),
        total: Number(order.total),
        items: order.items.map(item => ({
          ...item,
          price: Number(item.price),
          product: {
            ...item.product,
            priceUSD: Number(item.product.priceUSD),
            currentBid: item.product.currentBid ? Number(item.product.currentBid) : null,
          }
        }))
      };

      return successResponse("Order retrieved successfully", response);
    } catch (error) {
      console.error("Get order service error:", error);
      return errorResponse("Failed to retrieve order");
    }
  }

  // Update order status (admin function)
  async updateOrderStatus(orderId: string, statusData: any) {
    try {
      const order = await prisma.order.update({
        where: { id: orderId },
        data: statusData,
        include: {
          items: {
            include: {
              product: {
                include: { images: true }
              },
              bid: true // Include bid information for auction orders
            }
          },
          shippingAddress: true
        }
      });

      const response = {
        ...order,
        subtotal: Number(order.subtotal),
        shippingCost: Number(order.shippingCost),
        tax: Number(order.tax),
        total: Number(order.total),
        items: order.items.map(item => ({
          ...item,
          price: Number(item.price),
          product: {
            ...item.product,
            priceUSD: Number(item.product.priceUSD),
            currentBid: item.product.currentBid ? Number(item.product.currentBid) : null,
          }
        }))
      };

      return successResponse("Order status updated successfully", response);
    } catch (error) {
      console.error("Update order status service error:", error);
      return errorResponse("Failed to update order status");
    }
  }
}

const checkoutService = new CheckoutService();
export default checkoutService;
