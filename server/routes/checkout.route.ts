import { OpenAPIHono, createRoute, z } from "@hono/zod-openapi";
import { errorResponse } from "../utils/response.util";
import { formatZodError } from "../utils/format-zod-error.util";
import { 
  shippingAddressSchema,
  updateShippingAddressSchema,
  createOrderSchema,
  buyNowOrderSchema,
  updateOrderStatusSchema,
  ordersQuerySchema,
  orderResponseSchema,
  shippingAddressResponseSchema
} from "../schemas/checkout.schema";
import checkoutController from "../controllers/checkout.controller";
import { authMiddleware } from "../middlewares/auth";

const checkoutRoutes = new OpenAPIHono({
  defaultHook: (result, c) => {
    if (!result.success) {
      return c.json(
        errorResponse("Validation failed", formatZodError(result.error)),
        422
      );
    }
  },
});

// Checkout validation route
const validateCheckoutRoute = createRoute({
  method: "post",
  path: "/validate",
  request: {
    body: {
      content: {
        "application/json": {
          schema: z.object({
            checkoutType: z.enum(["cart", "buy-now", "bidding"]),
            productId: z.string().uuid().optional(),
            bidId: z.string().uuid().optional(),
          }),
        },
      },
      required: true,
    },
  },
  responses: {
    200: {
      description: "Checkout validation successful",
      content: {
        "application/json": {
          schema: z.object({
            status: z.boolean(),
            message: z.string(),
            data: z.any(),
          }),
        }
      }
    },
    400: {
      description: "Validation failed",
      content: {
        "application/json": {
          schema: z.object({
            status: z.boolean(),
            message: z.string(),
            data: z.any().optional(),
          }),
        }
      }
    },
    401: {
      description: "Unauthorized",
    },
  },
});

// Shipping Address Routes
const createShippingAddressRoute = createRoute({
  method: "post",
  path: "/shipping-addresses",
  request: {
    body: {
      content: {
        "application/json": {
          schema: shippingAddressSchema.openapi("ShippingAddressSchema"),
        },
      },
      required: true,
    },
  },
  responses: {
    201: {
      description: "Shipping address created successfully",
      content: {
        "application/json": {
          schema: z.object({
            status: z.boolean(),
            message: z.string(),
            data: shippingAddressResponseSchema,
          }),
        }
      }
    },
    400: {
      description: "Bad request",
    },
    401: {
      description: "Unauthorized",
    },
  },
});

const getShippingAddressesRoute = createRoute({
  method: "get",
  path: "/shipping-addresses",
  responses: {
    200: {
      description: "Shipping addresses retrieved successfully",
      content: {
        "application/json": {
          schema: z.object({
            status: z.boolean(),
            message: z.string(),
            data: z.array(shippingAddressResponseSchema),
          }),
        }
      }
    },
    401: {
      description: "Unauthorized",
    },
  },
});

const updateShippingAddressRoute = createRoute({
  method: "put",
  path: "/shipping-addresses/{addressId}",
  request: {
    params: z.object({
      addressId: z.string().uuid("Invalid address ID"),
    }),
    body: {
      content: {
        "application/json": {
          schema: updateShippingAddressSchema.openapi("UpdateShippingAddressSchema"),
        },
      },
      required: true,
    },
  },
  responses: {
    200: {
      description: "Shipping address updated successfully",
      content: {
        "application/json": {
          schema: z.object({
            status: z.boolean(),
            message: z.string(),
            data: shippingAddressResponseSchema,
          }),
        }
      }
    },
    400: {
      description: "Bad request",
    },
    401: {
      description: "Unauthorized",
    },
  },
});

const deleteShippingAddressRoute = createRoute({
  method: "delete",
  path: "/shipping-addresses/{addressId}",
  request: {
    params: z.object({
      addressId: z.string().uuid("Invalid address ID"),
    }),
  },
  responses: {
    200: {
      description: "Shipping address deleted successfully",
      content: {
        "application/json": {
          schema: z.object({
            status: z.boolean(),
            message: z.string(),
          }),
        }
      }
    },
    400: {
      description: "Bad request",
    },
    401: {
      description: "Unauthorized",
    },
  },
});

// Order Routes
const createOrderFromCartRoute = createRoute({
  method: "post",
  path: "/orders",
  request: {
    body: {
      content: {
        "application/json": {
          schema: createOrderSchema.openapi("CreateOrderSchema"),
        },
      },
      required: true,
    },
  },
  responses: {
    201: {
      description: "Order created successfully",
      content: {
        "application/json": {
          schema: z.object({
            status: z.boolean(),
            message: z.string(),
            data: orderResponseSchema,
          }),
        }
      }
    },
    400: {
      description: "Bad request",
    },
    401: {
      description: "Unauthorized",
    },
  },
});

const createBuyNowOrderRoute = createRoute({
  method: "post",
  path: "/orders/buy-now",
  request: {
    body: {
      content: {
        "application/json": {
          schema: buyNowOrderSchema.openapi("BuyNowOrderSchema"),
        },
      },
      required: true,
    },
  },
  responses: {
    201: {
      description: "Buy now order created successfully",
      content: {
        "application/json": {
          schema: z.object({
            status: z.boolean(),
            message: z.string(),
            data: orderResponseSchema,
          }),
        }
      }
    },
    400: {
      description: "Bad request",
    },
    401: {
      description: "Unauthorized",
    },
  },
});

const getOrdersRoute = createRoute({
  method: "get",
  path: "/orders",
  request: {
    query: ordersQuerySchema,
  },
  responses: {
    200: {
      description: "Orders retrieved successfully",
      content: {
        "application/json": {
          schema: z.object({
            status: z.boolean(),
            message: z.string(),
            data: z.object({
              orders: z.array(orderResponseSchema),
              pagination: z.object({
                page: z.number(),
                limit: z.number(),
                total: z.number(),
                totalPages: z.number(),
              }),
            }),
          }),
        }
      }
    },
    401: {
      description: "Unauthorized",
    },
  },
});

const getOrderRoute = createRoute({
  method: "get",
  path: "/orders/{orderId}",
  request: {
    params: z.object({
      orderId: z.string().uuid("Invalid order ID"),
    }),
  },
  responses: {
    200: {
      description: "Order retrieved successfully",
      content: {
        "application/json": {
          schema: z.object({
            status: z.boolean(),
            message: z.string(),
            data: orderResponseSchema,
          }),
        }
      }
    },
    404: {
      description: "Order not found",
    },
    401: {
      description: "Unauthorized",
    },
  },
});

const updateOrderStatusRoute = createRoute({
  method: "put",
  path: "/orders/{orderId}/status",
  request: {
    params: z.object({
      orderId: z.string().uuid("Invalid order ID"),
    }),
    body: {
      content: {
        "application/json": {
          schema: updateOrderStatusSchema.openapi("UpdateOrderStatusSchema"),
        },
      },
      required: true,
    },
  },
  responses: {
    200: {
      description: "Order status updated successfully",
      content: {
        "application/json": {
          schema: z.object({
            status: z.boolean(),
            message: z.string(),
            data: orderResponseSchema,
          }),
        }
      }
    },
    400: {
      description: "Bad request",
    },
    401: {
      description: "Unauthorized",
    },
    403: {
      description: "Forbidden",
    },
  },
});

// Apply auth middleware to all routes
checkoutRoutes.use('/*', authMiddleware);

// Register routes
checkoutRoutes.openapi(validateCheckoutRoute, checkoutController.validateCheckout);

checkoutRoutes.openapi(createShippingAddressRoute, checkoutController.createShippingAddress);
checkoutRoutes.openapi(getShippingAddressesRoute, checkoutController.getShippingAddresses);
checkoutRoutes.openapi(updateShippingAddressRoute, checkoutController.updateShippingAddress);
checkoutRoutes.openapi(deleteShippingAddressRoute, checkoutController.deleteShippingAddress);

checkoutRoutes.openapi(createOrderFromCartRoute, checkoutController.createOrderFromCart);
checkoutRoutes.openapi(createBuyNowOrderRoute, checkoutController.createBuyNowOrder);
// Use the unified createOrder endpoint for bidding orders
checkoutRoutes.post('/orders', authMiddleware, checkoutController.createOrder);
checkoutRoutes.openapi(getOrdersRoute, checkoutController.getOrders);
checkoutRoutes.openapi(getOrderRoute, checkoutController.getOrder);
checkoutRoutes.openapi(updateOrderStatusRoute, checkoutController.updateOrderStatus);

export { checkoutRoutes };
