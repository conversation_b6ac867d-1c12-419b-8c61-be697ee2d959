import { OpenAPIHono, createRoute, z } from '@hono/zod-openapi';
import userController from '../controllers/user.controller';
import { authMiddleware } from '../middlewares/auth';

const userRoutes = new OpenAPIHono();

// Update Profile Route
const updateProfileRoute = createRoute({
  method: 'put',
  path: '/profile',
  tags: ['User'],
  summary: 'Update user profile',
  description: 'Update user profile information including name, phone, and seller address',
  security: [{ bearerAuth: [] }],
  request: {
    body: {
      content: {
        'application/json': {
          schema: z.object({
            firstName: z.string().min(1, 'First name is required'),
            lastName: z.string().min(1, 'Last name is required'),
            phoneNumber: z.string().min(1, 'Phone number is required'),
            sellerAddress: z.string().optional(),
          }),
        },
      },
    },
  },
  responses: {
    200: {
      description: 'Profile updated successfully',
      content: {
        'application/json': {
          schema: z.object({
            status: z.boolean(),
            message: z.string(),
            data: z.object({
              id: z.string(),
              firstName: z.string(),
              lastName: z.string(),
              email: z.string(),
              phoneNumber: z.string(),
              sellerAddress: z.string().nullable(),
              emailVerified: z.boolean(),
              phoneVerified: z.boolean(),
              createdAt: z.string(),
              updatedAt: z.string(),
            }),
          }),
        },
      },
    },
    400: {
      description: 'Bad request',
      content: {
        'application/json': {
          schema: z.object({
            status: z.boolean(),
            message: z.string(),
          }),
        },
      },
    },
    401: {
      description: 'Unauthorized',
    },
    404: {
      description: 'User not found',
    },
  },
});

// Get Seller Status Route
const getSellerStatusRoute = createRoute({
  method: 'get',
  path: '/seller-status',
  tags: ['User'],
  summary: 'Get seller verification status',
  description: 'Get user seller verification status and requirements',
  security: [{ bearerAuth: [] }],
  responses: {
    200: {
      description: 'Seller status retrieved successfully',
      content: {
        'application/json': {
          schema: z.object({
            status: z.boolean(),
            message: z.string(),
            data: z.object({
              canSell: z.boolean(),
              emailVerified: z.boolean(),
              phoneVerified: z.boolean(),
              hasPhoneNumber: z.boolean(),
              hasSellerAddress: z.boolean(),
              missingRequirements: z.array(z.string()),
            }),
          }),
        },
      },
    },
    401: {
      description: 'Unauthorized',
    },
    404: {
      description: 'User not found',
    },
  },
});

// Request Verification Route
const requestVerificationRoute = createRoute({
  method: 'post',
  path: '/verification/request',
  tags: ['User'],
  summary: 'Request verification code',
  description: 'Request verification code for email or phone',
  security: [{ bearerAuth: [] }],
  request: {
    body: {
      content: {
        'application/json': {
          schema: z.object({
            type: z.enum(['email', 'phone']),
            value: z.string().min(1, 'Value is required'),
          }),
        },
      },
    },
  },
  responses: {
    200: {
      description: 'Verification code sent successfully',
      content: {
        'application/json': {
          schema: z.object({
            status: z.boolean(),
            message: z.string(),
            data: z.object({
              type: z.string(),
              expiresAt: z.string(),
              message: z.string(),
            }),
          }),
        },
      },
    },
    400: {
      description: 'Bad request',
    },
    401: {
      description: 'Unauthorized',
    },
  },
});

// Confirm Verification Route
const confirmVerificationRoute = createRoute({
  method: 'post',
  path: '/verification/confirm',
  tags: ['User'],
  summary: 'Confirm verification code',
  description: 'Confirm verification code for email or phone',
  security: [{ bearerAuth: [] }],
  request: {
    body: {
      content: {
        'application/json': {
          schema: z.object({
            type: z.enum(['email', 'phone']),
            code: z.string().min(6, 'Code must be 6 digits').max(6, 'Code must be 6 digits'),
          }),
        },
      },
    },
  },
  responses: {
    200: {
      description: 'Verification completed successfully',
      content: {
        'application/json': {
          schema: z.object({
            status: z.boolean(),
            message: z.string(),
            data: z.object({
              type: z.string(),
              verified: z.boolean(),
            }),
          }),
        },
      },
    },
    400: {
      description: 'Invalid or expired verification code',
    },
    401: {
      description: 'Unauthorized',
    },
  },
});

// Get User Addresses Route
const getUserAddressesRoute = createRoute({
  method: 'get',
  path: '/addresses',
  tags: ['User'],
  summary: 'Get user shipping addresses',
  description: 'Get all shipping addresses for the authenticated user',
  security: [{ bearerAuth: [] }],
  responses: {
    200: {
      description: 'Addresses retrieved successfully',
      content: {
        'application/json': {
          schema: z.object({
            status: z.boolean(),
            message: z.string(),
            data: z.array(z.object({
              id: z.string(),
              name: z.string(),
              country: z.string(),
              province: z.string(),
              city: z.string(),
              postalCode: z.string(),
              fullAddress: z.string(),
              isDefault: z.boolean(),
              createdAt: z.string(),
              updatedAt: z.string(),
            })),
          }),
        },
      },
    },
    401: {
      description: 'Unauthorized',
    },
  },
});

// Create User Address Route
const createUserAddressRoute = createRoute({
  method: 'post',
  path: '/addresses',
  tags: ['User'],
  summary: 'Create user shipping address',
  description: 'Create a new shipping address for the authenticated user',
  security: [{ bearerAuth: [] }],
  request: {
    body: {
      content: {
        'application/json': {
          schema: z.object({
            name: z.string().min(1, 'Address name is required'),
            country: z.string().min(1, 'Country is required'),
            province: z.string().min(1, 'Province is required'),
            city: z.string().min(1, 'City is required'),
            postalCode: z.string().min(1, 'Postal code is required'),
            fullAddress: z.string().min(1, 'Full address is required'),
            isDefault: z.boolean().optional().default(false),
          }),
        },
      },
    },
  },
  responses: {
    201: {
      description: 'Address created successfully',
      content: {
        'application/json': {
          schema: z.object({
            status: z.boolean(),
            message: z.string(),
            data: z.object({
              id: z.string(),
              name: z.string(),
              country: z.string(),
              province: z.string(),
              city: z.string(),
              postalCode: z.string(),
              fullAddress: z.string(),
              isDefault: z.boolean(),
            }),
          }),
        },
      },
    },
    400: {
      description: 'Bad request',
    },
    401: {
      description: 'Unauthorized',
    },
  },
});

// Apply auth middleware to all routes
userRoutes.use('/*', authMiddleware);

// Register routes
userRoutes.openapi(updateProfileRoute, userController.updateProfile);
userRoutes.openapi(getSellerStatusRoute, userController.getSellerStatus);
userRoutes.openapi(requestVerificationRoute, userController.requestVerification);
userRoutes.openapi(confirmVerificationRoute, userController.confirmVerification);
userRoutes.openapi(getUserAddressesRoute, userController.getUserAddresses);
userRoutes.openapi(createUserAddressRoute, userController.createUserAddress);

export { userRoutes };
