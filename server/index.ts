import { errorResponse } from "./utils/response.util";
import { OpenAPIHono } from "@hono/zod-openapi";
import { prisma } from "./db";
import { authRoutes } from "./routes/auth.route";
import { productRoutes } from "./routes/product.route";
import { masterRoutes } from "./routes/master.route";
import { cartRoutes } from "./routes/cart.route";
import { checkoutRoutes } from "./routes/checkout.route";
import { orderRoutes } from "./routes/order.route";
import { biddingRoutes } from "./routes/bidding.route";
import { currencyRoutes } from "./routes/currency.route";
import shippingRoute from "./routes/shipping.route";
import autoBidRoute from "./routes/autoBid.route";
import dailyCurrencyRateRoute from "./routes/dailyCurrencyRate.route";
import orderTrackingRoute from "./routes/orderTracking.route";
import productPricingRoute from "./routes/productPricing.route";
import { enableSwaggerUi, swaggerDocs } from "./docs/swagger";
import currencyRateScheduler from "./jobs/currencyRateScheduler";
import auctionScheduler from "./jobs/auctionScheduler";
// import { jwt } from 'hono/jwt'
import { jwt, type JwtVariables } from 'hono/jwt'
import { paymentRoutes } from "./routes/payment.route";
import bidHistoryRoutes from "./routes/bidHistory.route";
import { auctionExtensionRoutes } from "./routes/auctionExtension.route";
import { auctionRoutes } from "./routes/auction.route";
import { userRoutes } from "./routes/user.route";
import emailService from "./services/email.service";
import {
  securityHeadersMiddleware,
  enhancedRateLimitMiddleware,
  requestSizeLimitMiddleware,
  securityLoggingMiddleware
} from "./middlewares/security";

type Variables = JwtVariables

const app = new OpenAPIHono<{ Variables: Variables }>().basePath("/api/v1");

// Apply security middleware globally
app.use('*', securityHeadersMiddleware());
app.use('*', securityLoggingMiddleware());
app.use('*', requestSizeLimitMiddleware(50 * 1024 * 1024)); // 50MB limit

// Apply rate limiting to different endpoint types
app.use('/auth/*', enhancedRateLimitMiddleware('auth', 20, 15 * 60 * 1000)); // 20 requests per 15 minutes for auth
app.use('/products/upload/*', enhancedRateLimitMiddleware('upload', 10, 60 * 1000)); // 10 uploads per minute
app.use('*', enhancedRateLimitMiddleware('api', 200, 15 * 60 * 1000)); // 200 requests per 15 minutes for general API

app.notFound((c) => {
  return c.json(errorResponse("Not Found"), 404);
});

app.route("/auth", authRoutes);
app.route("/products", productRoutes);
app.route("/master", masterRoutes);
app.route("/cart", cartRoutes);
app.route("/checkout", checkoutRoutes);
app.route("/orders", orderRoutes);
app.route("/order-tracking", orderTrackingRoute);
app.route("/bidding", biddingRoutes);
app.route("/payments", paymentRoutes);
app.route("/currency", currencyRoutes);
app.route("/currency-rates", dailyCurrencyRateRoute);
app.route("/product-pricing", productPricingRoute);
app.route("/shipping", shippingRoute);
app.route("/auto-bid", autoBidRoute);
app.route("/bid-history", bidHistoryRoutes);
app.route("/auction-extensions", auctionExtensionRoutes);
app.route("/auction", auctionRoutes);
app.route("/user", userRoutes);

app.get("/check-db", async (c) => {
  try {
    await prisma.$queryRaw`SELECT 1`;
    return c.json({ status: "ok", db: true });
  } catch (err) {
    console.error("DB Error:", err);
    return c.json({ status: err, db: false }, 500);
  }
});

app.get("/check-email", async (c) => {
  try {
    const isReady = emailService.isReady();

    if (!isReady) {
      return c.json({
        status: "not configured",
        email: false,
        message: "Email service not configured - missing SMTP credentials"
      }, 503);
    }

    const config = {
      host: process.env.SMTP_HOST || 'not configured',
      port: process.env.SMTP_PORT || 'not configured',
      user: process.env.SMTP_USER ? 'configured' : 'not configured',
      pass: process.env.SMTP_PASS ? 'configured' : 'not configured',
    };

    return c.json({
      status: "ok",
      email: true,
      config: config,
      timestamp: new Date().toISOString()
    });
  } catch (err) {
    console.error("Email Service Error:", err);
    return c.json({
      status: "error",
      email: false,
      error: err instanceof Error ? err.message : 'Unknown error'
    }, 500);
  }
});

app.post("/test-email", async (c) => {
  try {
    const body = await c.req.json();
    const { email } = body;

    if (!email) {
      return c.json({
        status: "error",
        message: "Email address is required"
      }, 400);
    }

    if (!emailService.isReady()) {
      return c.json({
        status: "error",
        message: "Email service not configured"
      }, 503);
    }

    const result = await emailService.sendTestEmail(email);

    if (result.success) {
      return c.json({
        status: "success",
        message: "Test email sent successfully",
        data: result.data
      });
    } else {
      return c.json({
        status: "error",
        message: result.message,
        error: result.error
      }, 500);
    }
  } catch (err) {
    console.error("Test Email Error:", err);
    return c.json({
      status: "error",
      message: "Failed to send test email",
      error: err instanceof Error ? err.message : 'Unknown error'
    }, 500);
  }
});

app.doc("/swagger.json", {
  openapi: "3.0.0",
  info: {
    title: "Service Report API",
    version: "1.0.0",
    description: "API documentation for service reports",
  },
});

enableSwaggerUi({ app, uiPath: "/docs", docPath: "/api/v1/swagger.json" });

app.get("/docs", swaggerDocs);

// Manual auction scheduler endpoints for debugging/testing
app.get("/auction-scheduler/status", async (c) => {
  try {
    const status = auctionScheduler.getStatus();
    const stats = await auctionScheduler.getAuctionStats();
    return c.json({
      status: "ok",
      scheduler: status,
      stats: stats,
      timestamp: new Date().toISOString()
    });
  } catch (err) {
    console.error("Auction Scheduler Status Error:", err);
    return c.json({
      status: "error",
      message: "Failed to get auction scheduler status",
      error: err instanceof Error ? err.message : 'Unknown error'
    }, 500);
  }
});

app.post("/auction-scheduler/force-check", async (c) => {
  try {
    console.log("🔄 Manual force check triggered via API");
    const result = await auctionScheduler.forceCheck();
    return c.json({
      status: "ok",
      message: "Force check completed",
      result: result,
      timestamp: new Date().toISOString()
    });
  } catch (err) {
    console.error("Auction Scheduler Force Check Error:", err);
    return c.json({
      status: "error",
      message: "Failed to force check auctions",
      error: err instanceof Error ? err.message : 'Unknown error'
    }, 500);
  }
});

app.get("/auction-scheduler/debug", async (c) => {
  try {
    const { getJakartaTime } = await import('./utils/timezone.util');
    const now = new Date();
    const jakartaTime = getJakartaTime();

    // Get active auctions with their end dates
    const activeAuctions = await prisma.product.findMany({
      where: {
        sellType: "auction",
        isActive: true,
        status: "active",
      },
      select: {
        id: true,
        itemName: true,
        auctionStartDate: true,
        auctionEndDate: true,
        auctionCompleted: true,
        auctionCompletedAt: true,
        _count: {
          select: {
            bids: true
          }
        }
      },
      orderBy: {
        auctionEndDate: 'asc'
      }
    });

    const endedButNotCompleted = activeAuctions.filter(auction =>
      auction.auctionEndDate &&
      auction.auctionEndDate <= jakartaTime &&
      !auction.auctionCompleted
    );

    return c.json({
      status: "ok",
      debug: {
        currentTime: now.toISOString(),
        jakartaTime: jakartaTime.toISOString(),
        totalActiveAuctions: activeAuctions.length,
        endedButNotCompleted: endedButNotCompleted.length,
        activeAuctions: activeAuctions.map(auction => ({
          id: auction.id,
          itemName: auction.itemName,
          auctionStartDate: auction.auctionStartDate?.toISOString(),
          auctionEndDate: auction.auctionEndDate?.toISOString(),
          auctionCompleted: auction.auctionCompleted,
          auctionCompletedAt: auction.auctionCompletedAt?.toISOString(),
          bidCount: auction._count.bids,
          hasEnded: auction.auctionEndDate ? auction.auctionEndDate <= jakartaTime : false,
          timeUntilEnd: auction.auctionEndDate ?
            Math.max(0, auction.auctionEndDate.getTime() - jakartaTime.getTime()) : null
        })),
        endedButNotCompletedDetails: endedButNotCompleted
      },
      timestamp: new Date().toISOString()
    });
  } catch (err) {
    console.error("Auction Scheduler Debug Error:", err);
    return c.json({
      status: "error",
      message: "Failed to get debug info",
      error: err instanceof Error ? err.message : 'Unknown error'
    }, 500);
  }
});

app.post("/auction-scheduler/process-auction/:auctionId", async (c) => {
  try {
    const auctionId = c.req.param('auctionId');

    if (!auctionId) {
      return c.json({
        status: "error",
        message: "Auction ID is required"
      }, 400);
    }

    // Get the auction details
    const auction = await prisma.product.findUnique({
      where: { id: auctionId },
      include: {
        bids: {
          orderBy: { amount: "desc" },
          take: 1,
          include: {
            bidder: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                email: true,
              },
            },
          },
        },
        images: {
          where: { isMain: true },
          take: 1,
          orderBy: { sortOrder: "asc" },
        },
      },
    });

    if (!auction) {
      return c.json({
        status: "error",
        message: "Auction not found"
      }, 404);
    }

    if (auction.sellType !== "auction") {
      return c.json({
        status: "error",
        message: "Product is not an auction"
      }, 400);
    }

    if (auction.auctionCompleted) {
      return c.json({
        status: "error",
        message: "Auction already completed"
      }, 400);
    }

    // Manually process this auction (for testing)
    console.log(`🔄 Manually processing auction: ${auction.itemName}`);

    // Force complete the auction for testing
    const result = await prisma.$transaction(async (tx) => {
      if (auction.bids.length > 0) {
        const winningBid = auction.bids[0];
        const winner = winningBid.bidder;

        // Mark auction as completed and set winner information
        await tx.product.update({
          where: { id: auction.id },
          data: {
            auctionCompleted: true,
            auctionCompletedAt: new Date(),
            winnerId: winner.id,
            winnerBidId: winningBid.id,
          },
        });

        // Mark the winning bid
        await tx.bid.update({
          where: { id: winningBid.id },
          data: {
            isWinning: true,
            auctionWon: true,
          },
        });

        // Mark all other bids as not winning
        await tx.bid.updateMany({
          where: {
            productId: auction.id,
            id: { not: winningBid.id },
          },
          data: {
            isWinning: false,
            auctionWon: false,
          },
        });

        return {
          hasWinner: true,
          winningBid,
          winner,
        };
      } else {
        // Mark auction as completed without winner
        await tx.product.update({
          where: { id: auction.id },
          data: {
            auctionCompleted: true,
            auctionCompletedAt: new Date(),
          },
        });

        return { hasWinner: false };
      }
    });

    return c.json({
      status: "ok",
      message: "Auction processed manually",
      data: {
        auctionId: auction.id,
        itemName: auction.itemName,
        hasWinner: result.hasWinner,
        winner: result.hasWinner ? {
          id: result.winner?.id,
          name: `${result.winner?.firstName || ""} ${result.winner?.lastName || ""}`.trim(),
          email: result.winner?.email,
          winningBid: Number(result.winningBid?.amount || 0)
        } : null
      },
      timestamp: new Date().toISOString()
    });

  } catch (err) {
    console.error("Manual Auction Processing Error:", err);
    return c.json({
      status: "error",
      message: "Failed to process auction manually",
      error: err instanceof Error ? err.message : 'Unknown error'
    }, 500);
  }
});

app.post("/auction-scheduler/trigger-initial-check", async (c) => {
  try {
    console.log("🔄 Manual initial check triggered via API");

    // Access the private method through a public interface
    const result = await auctionScheduler.forceCheck();

    return c.json({
      status: "ok",
      message: "Initial check triggered",
      result: result,
      timestamp: new Date().toISOString()
    });
  } catch (err) {
    console.error("Manual Initial Check Error:", err);
    return c.json({
      status: "error",
      message: "Failed to trigger initial check",
      error: err instanceof Error ? err.message : 'Unknown error'
    }, 500);
  }
});

// Start currency rate scheduler (only if enabled)
if (process.env.ENABLE_CURRENCY_SCHEDULER !== 'false') {
  console.log('🚀 Starting currency rate scheduler...');
  currencyRateScheduler.start();
} else {
  console.log('⏸️ Currency rate scheduler disabled via environment variable');
}

// Start auction end detection scheduler (only if enabled)
if (process.env.ENABLE_AUCTION_SCHEDULER !== 'false') {
  console.log('🚀 Starting auction end detection scheduler...');
  try {
    auctionScheduler.start();
    console.log('✅ Auction scheduler started successfully');
  } catch (error) {
    console.error('❌ Failed to start auction scheduler:', error);
  }
} else {
  console.log('⏸️ Auction scheduler disabled via environment variable');
}

export { app };
