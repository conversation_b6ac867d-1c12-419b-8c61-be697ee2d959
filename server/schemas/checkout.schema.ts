import { z } from "zod";

export const shippingAddressSchema = z.object({
  name: z.string().min(1, "Name is required").max(100),
  address: z.string().min(1, "Address is required").max(500),
  city: z.string().min(1, "City is required").max(100),
  provinceRegion: z.string().min(1, "Province/Region is required").max(100),
  zipCode: z.string().min(1, "Zip code is required").max(20),
  country: z.string().min(1, "Country is required").max(100),
  isDefault: z.boolean().optional().default(false),
});

export const updateShippingAddressSchema = shippingAddressSchema.partial();

// Enhanced payment method schema
const paymentMethodSchema = z.object({
  type: z.enum(["invoice", "ewallet", "virtual_account", "retail_outlet", "credit_card"]),
  method: z.string().optional(),
  channel: z.string().optional(),
  ewalletType: z.string().optional(),
  bankCode: z.string().optional(),
  retailOutletName: z.string().optional(),
});

export const createOrderSchema = z.object({
  orderType: z.enum(["cart", "buy-now", "bidding"]).optional(),
  currency: z.enum(["USD", "IDR"]).default("USD"),
  shippingAddressId: z.string().uuid("Invalid shipping address ID").optional(),
  shippingAddress: shippingAddressSchema.optional(),
  paymentMethod: paymentMethodSchema,
  notes: z.string().max(1000).optional(),
  // Cart checkout specific fields
  products: z.array(z.string().uuid()).optional(),
  cartItems: z.array(z.object({
    productId: z.string().uuid(),
    quantity: z.number().int().positive(),
    price: z.number().positive(),
  })).optional(),
  // Buy-now checkout specific fields
  productId: z.string().uuid().optional(),
  quantity: z.number().int().positive().optional(),
  // Bidding checkout specific fields
  bidId: z.string().uuid().optional(),
  winningBid: z.number().positive().optional(),
}).refine(
  (data) => data.shippingAddressId || data.shippingAddress,
  {
    message: "Either shippingAddressId or shippingAddress must be provided",
    path: ["shippingAddress"],
  }
);

export const buyNowOrderSchema = z.object({
  orderType: z.enum(["buy-now"]).default("buy-now"),
  currency: z.enum(["USD", "IDR"]).default("USD"),
  productId: z.string().uuid("Invalid product ID"),
  quantity: z.number().int().positive().default(1),
  shippingAddressId: z.string().uuid("Invalid shipping address ID").optional(),
  shippingAddress: shippingAddressSchema.optional(),
  paymentMethod: paymentMethodSchema,
  notes: z.string().max(1000).optional(),
}).refine(
  (data) => data.shippingAddressId || data.shippingAddress,
  {
    message: "Either shippingAddressId or shippingAddress must be provided",
    path: ["shippingAddress"],
  }
);

export const updateOrderStatusSchema = z.object({
  status: z.enum(["pending", "processing", "shipped", "delivered", "cancelled"]),
  paymentStatus: z.enum(["pending", "paid", "failed", "refunded"]).optional(),
});

export const orderItemResponseSchema = z.object({
  id: z.string().uuid(),
  orderId: z.string().uuid(),
  productId: z.string().uuid(),
  quantity: z.number().int(),
  price: z.number(),
  bidId: z.string().uuid().nullable(),
  product: z.object({
    id: z.string().uuid(),
    itemName: z.string(),
    slug: z.string().nullable(),
    priceUSD: z.number(),
    sellType: z.string(),
    images: z.array(z.object({
      id: z.string().uuid(),
      imageUrl: z.string(),
      isMain: z.boolean(),
    })),
  }),
  createdAt: z.string().datetime(),
});

export const shippingAddressResponseSchema = z.object({
  id: z.string().uuid(),
  userId: z.string().uuid(),
  name: z.string(),
  address: z.string(),
  city: z.string(),
  provinceRegion: z.string(),
  zipCode: z.string(),
  country: z.string(),
  isDefault: z.boolean(),
  createdAt: z.string().datetime(),
  updatedAt: z.string().datetime(),
});

export const orderResponseSchema = z.object({
  id: z.string().uuid(),
  userId: z.string().uuid(),
  orderNumber: z.string(),
  status: z.enum(["pending", "processing", "shipped", "delivered", "cancelled"]),
  paymentStatus: z.enum(["pending", "paid", "failed", "refunded"]),
  paymentMethod: z.string().nullable(),
  subtotal: z.number(),
  shippingCost: z.number(),
  tax: z.number(),
  total: z.number(),
  shippingAddressId: z.string().uuid().nullable(),
  bidId: z.string().uuid().nullable(),
  notes: z.string().nullable(),
  shippingAddress: shippingAddressResponseSchema.nullable(),
  items: z.array(orderItemResponseSchema),
  createdAt: z.string().datetime(),
  updatedAt: z.string().datetime(),
});

export const ordersQuerySchema = z.object({
  page: z.string().transform(Number).pipe(z.number().int().min(1)).default("1"),
  limit: z.string().transform(Number).pipe(z.number().int().min(1).max(100)).default("10"),
  status: z.enum(["pending", "processing", "shipped", "delivered", "cancelled"]).optional(),
  paymentStatus: z.enum(["pending", "paid", "failed", "refunded"]).optional(),
  sortBy: z.enum(["createdAt", "total", "status"]).default("createdAt"),
  sortOrder: z.enum(["asc", "desc"]).default("desc"),
});
