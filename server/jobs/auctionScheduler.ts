import cron from "node-cron";
import notificationService from "../services/notification.service";
import webSocketService from "../services/websocket.service";
import { successResponse, errorResponse } from "../utils/response.util";
import { prisma } from "../db";
import { getJakartaTime } from "../utils/timezone.util";

interface AuctionWinner {
  productId: string;
  productTitle: string;
  productSlug: string;
  productImage: string;
  winnerId: string;
  winnerEmail: string;
  winnerName: string;
  winningBid: number;
  auctionEndDate: string;
  bidId: string;
}

interface ProcessedAuction {
  productId: string;
  title: string;
  endDate: Date | null;
  hasBids: boolean;
  winningBid: number;
}

async function notifyWebSocketServer(endpoint: string, data: any) {
  try {
    const wsUrl = process.env.NEXT_PUBLIC_WS_URL || "ws://localhost:3001";
    const httpUrl = wsUrl
      .replace("ws://", "http://")
      .replace("wss://", "https://");

    console.log(`📡 Sending WebSocket notification to ${httpUrl}${endpoint}`);
    console.log("📦 Notification data:", data);

    // Add timeout and retry logic
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 5000); // 5 second timeout

    try {
      const response = await fetch(`${httpUrl}${endpoint}`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const result = await response.json();
      console.log("✅ WebSocket notification sent successfully:", result);
      return result;
    } catch (fetchError) {
      clearTimeout(timeoutId);
      throw fetchError;
    }
  } catch (error) {
    console.error("❌ Failed to send WebSocket notification:", error);

    // Don't throw the error - just log it and continue
    // This prevents the entire auction processing from failing due to notification issues
    console.log("⚠️ Continuing auction processing despite notification failure");
    return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
  }
}

class AuctionScheduler {
  private isRunning = false;
  private checkInterval = "*/2 * * * *"; // Every 2 minutes
  private lastCheckTime: Date | null = null;
  private totalProcessedAuctions = 0;
  private totalNotifiedWinners = 0;
  private lastError: string | null = null;

  /**
   * Start the auction scheduler
   */
  start() {
    console.log("🚀 Starting auction end detection scheduler...");
    console.log(`📅 Current time: ${new Date().toISOString()}`);
    console.log(`🌏 Jakarta time: ${getJakartaTime().toISOString()}`);

    try {
      // Check for ended auctions every 2 minutes
      const cronJob = cron.schedule(
        this.checkInterval,
        async () => {
          if (this.isRunning) {
            console.log("⏳ Auction check already running, skipping...");
            return;
          }

          this.isRunning = true;
          console.log(`🔍 Starting scheduled auction check at ${getJakartaTime().toISOString()}`);

          try {
            const result = await this.checkEndedAuctions();
            this.lastCheckTime = new Date();
            this.lastError = null;
            console.log(`✅ Scheduled auction check completed: ${result.message}`);

            if (result.data) {
              this.totalProcessedAuctions += result.data.processedCount || 0;
              this.totalNotifiedWinners += result.data.winnersCount || 0;
            }
          } catch (error) {
            this.lastError = error instanceof Error ? error.message : 'Unknown error';
            console.error("❌ Error during scheduled auction check:", error);
          } finally {
            this.isRunning = false;
          }
        },
        {
          timezone: "Asia/Jakarta"
        }
      );

      if (cronJob) {
        console.log("✅ Cron job scheduled successfully");
      } else {
        throw new Error("Failed to schedule cron job");
      }

      // Run initial check after 30 seconds
      setTimeout(() => {
        console.log("⏰ Running initial auction check in 30 seconds...");
        this.runInitialCheck();
      }, 30000);

      console.log("✅ Auction scheduler started successfully");
      console.log("🔄 Checking for ended auctions every 2 minutes");
      console.log(`📊 Next check scheduled for: ${this.getNextCheckTime()}`);

    } catch (error) {
      console.error("❌ Failed to start auction scheduler:", error);
      throw error;
    }
  }

  /**
   * Run initial check on startup
   */
  private async runInitialCheck() {
    if (this.isRunning) {
      console.log("⏳ Auction check already running, skipping initial check...");
      return;
    }

    this.isRunning = true;
    console.log("🔄 Running initial auction end check...");
    console.log(`📅 Initial check time: ${getJakartaTime().toISOString()}`);

    try {
      const startTime = Date.now();
      const result = await this.checkEndedAuctions();
      const duration = Date.now() - startTime;

      this.lastCheckTime = new Date();
      this.lastError = null;

      console.log(`✅ Initial auction check completed in ${duration}ms`);
      console.log(`📊 Initial check result: ${result.message}`);

      if (result.data) {
        this.totalProcessedAuctions += result.data.processedCount || 0;
        this.totalNotifiedWinners += result.data.winnersCount || 0;
        console.log(`📊 Processed auctions: ${result.data.processedCount || 0}`);
        console.log(`📊 Winners notified: ${result.data.winnersCount || 0}`);
      }
    } catch (error) {
      this.lastError = error instanceof Error ? error.message : 'Unknown error';
      console.error("❌ Error during initial auction check:", error);
      console.error("❌ Stack trace:", error instanceof Error ? error.stack : 'No stack trace');
    } finally {
      this.isRunning = false;
    }
  }

  /**
   * Check for auctions that have ended and process winners
   */
  async checkEndedAuctions(): Promise<any> {
    try {
      // Get current time in Jakarta timezone
      const now = getJakartaTime();
      console.log(`🔍 Checking for ended auctions at ${now.toISOString()}`);

      // Find auctions that have ended but haven't been processed yet
      const endedAuctions = await prisma.product.findMany({
        where: {
          sellType: "auction",
          auctionEndDate: {
            lte: now,
          },
          // Only process auctions that haven't been marked as completed
          auctionCompleted: false,
          // Only process active auctions
          isActive: true,
          status: "active",
        },
        include: {
          bids: {
            orderBy: { amount: "desc" },
            take: 1,
            include: {
              bidder: {
                select: {
                  id: true,
                  firstName: true,
                  lastName: true,
                  email: true,
                },
              },
            },
          },
          images: {
            where: { isMain: true },
            take: 1,
            orderBy: { sortOrder: "asc" },
          },
        },
      });

      console.log(`📊 Found ${endedAuctions.length} ended auctions to process`);

      if (endedAuctions.length === 0) {
        console.log("✅ No ended auctions found");
        return successResponse("No ended auctions to process", {
          processedCount: 0,
          winnersCount: 0,
          processedAuctions: [],
        });
      }

      console.log(`🔄 Processing ${endedAuctions.length} ended auction(s)...`);

      const processedAuctions: ProcessedAuction[] = [];
      const winners: AuctionWinner[] = [];

      // Process auctions in batches to avoid overwhelming the database
      const batchSize = 5;
      for (let i = 0; i < endedAuctions.length; i += batchSize) {
        const batch = endedAuctions.slice(i, i + batchSize);

        await Promise.allSettled(
          batch.map(async (auction) => {
            try {
              // Use transaction to ensure data consistency
              const result = await prisma.$transaction(async (tx) => {
                if (auction.bids.length > 0) {
                  const winningBid = auction.bids[0];
                  const winner = winningBid.bidder;

                  // Mark auction as completed and set winner information
                  await tx.product.update({
                    where: { id: auction.id },
                    data: {
                      auctionCompleted: true,
                      auctionCompletedAt: new Date(),
                      winnerId: winner.id,
                      winnerBidId: winningBid.id,
                    },
                  });

                  // Mark the winning bid
                  await tx.bid.update({
                    where: { id: winningBid.id },
                    data: {
                      isWinning: true,
                      auctionWon: true,
                    },
                  });

                  // Mark all other bids as not winning in a single query
                  await tx.bid.updateMany({
                    where: {
                      productId: auction.id,
                      id: { not: winningBid.id },
                    },
                    data: {
                      isWinning: false,
                      auctionWon: false,
                    },
                  });

                  return {
                    hasWinner: true,
                    winningBid,
                    winner,
                  };
                } else {
                  // Mark auction as completed without winner
                  await tx.product.update({
                    where: { id: auction.id },
                    data: {
                      auctionCompleted: true,
                      auctionCompletedAt: new Date(),
                    },
                  });
                }

                return { hasWinner: false };
              });

              // Process winner data outside transaction
              if (result.hasWinner && result.winningBid && result.winner) {
                const winnerData: AuctionWinner = {
                  productId: auction.id,
                  productTitle: auction.itemName,
                  productSlug: auction.slug || "",
                  productImage: auction.images[0]?.imageUrl || "",
                  winnerId: result.winner.id,
                  winnerEmail: result.winner.email || "",
                  winnerName:
                    `${result.winner.firstName || ""} ${result.winner.lastName || ""}`.trim(),
                  winningBid: Number(result.winningBid.amount),
                  auctionEndDate: auction.auctionEndDate?.toISOString() || "",
                  bidId: result.winningBid.id,
                };

                // Try to send WebSocket notification, but don't fail if it doesn't work
                try {
                  await notifyWebSocketServer("/notify/auction-ended", {
                    productId: auction.id,
                    extensionData: {
                      productId: auction.id,
                      triggeredAt: now.toISOString(),
                    },
                  });
                } catch (notificationError) {
                  console.error(`⚠️ WebSocket notification failed for auction ${auction.id}:`, notificationError);
                  // Continue processing - don't let notification failures stop auction completion
                }

                winners.push(winnerData);
                console.log(
                  `✅ Processed auction: ${auction.itemName} - Winner: ${winnerData.winnerName} ($${result.winningBid.amount})`
                );
              } else {
                console.log(
                  `⚠️ Auction ended with no bids: ${auction.itemName}`
                );
              }

              processedAuctions.push({
                productId: auction.id,
                title: auction.itemName,
                endDate: auction.auctionEndDate,
                hasBids: auction.bids.length > 0,
                winningBid: Number(auction.bids[0]?.amount || 0),
              });
            } catch (error) {
              console.error(
                `❌ Error processing auction ${auction.id}:`,
                error
              );
              // Revert the completion status if processing failed
              try {
                await prisma.product.update({
                  where: { id: auction.id },
                  data: { auctionCompleted: false },
                });
              } catch (revertError) {
                console.error(
                  `❌ Failed to revert auction completion status for ${auction.id}:`,
                  revertError
                );
              }
            }
          })
        );

        // Small delay between batches to prevent overwhelming the system
        if (i + batchSize < endedAuctions.length) {
          await new Promise((resolve) => setTimeout(resolve, 500));
        }
      }

      // Send winner notifications in parallel (but with rate limiting)
      if (winners.length > 0) {
        console.log(`📧 Processing notifications for ${winners.length} auction winners`);
        await this.notifyWinners(winners);
      } else {
        console.log("ℹ️ No auction winners to notify");
      }

      // Send WebSocket notifications about auction completions in batches
      if (processedAuctions.length > 0) {
        console.log(`📡 Sending WebSocket notifications for ${processedAuctions.length} completed auctions...`);

        const notificationPromises = processedAuctions.map((auction) =>
          Promise.resolve().then(async () => {
            try {
              webSocketService.notifyAuctionStatusChange(
                auction.productId,
                "ended",
                {
                  productId: auction.productId,
                  title: auction.title,
                  endDate: auction.endDate,
                  hasBids: auction.hasBids,
                  winningBid: auction.winningBid,
                }
              );
              console.log(`✅ WebSocket notification sent for auction ${auction.productId}`);
            } catch (error) {
              console.error(`⚠️ WebSocket notification failed for auction ${auction.productId}:`, error);
              // Don't throw - just log the error
            }
          })
        );

        // Execute WebSocket notifications in parallel with timeout
        const notificationResults = await Promise.allSettled(notificationPromises);
        const successCount = notificationResults.filter(result => result.status === 'fulfilled').length;
        const failureCount = notificationResults.filter(result => result.status === 'rejected').length;

        console.log(`📊 WebSocket notifications: ${successCount} successful, ${failureCount} failed`);
      }

      return successResponse("Auction check completed", {
        processedCount: processedAuctions.length,
        winnersCount: winners.length,
        processedAuctions,
      });
    } catch (error) {
      console.error("❌ Error checking ended auctions:", error);
      return errorResponse(
        "Failed to check ended auctions",
        error instanceof Error ? error.message : "Unknown error"
      );
    }
  }

  /**
   * Send notifications to auction winners with optimized batch processing
   */
  private async notifyWinners(winners: AuctionWinner[]): Promise<void> {
    console.log(
      `📧 Starting notification process for ${winners.length} auction winner(s)...`
    );

    // Process notifications sequentially to avoid overwhelming email service
    const successfulNotifications: string[] = [];
    const failedNotifications: string[] = [];

    for (let i = 0; i < winners.length; i++) {
      const winner = winners[i];

      try {
        console.log(`📧 Processing winner ${i + 1}/${winners.length}: ${winner.winnerEmail} for product "${winner.productTitle}"`);

        // Send winner notification
        const notificationResult = await notificationService.sendAuctionWinnerNotification({
          productId: winner.productId,
          productTitle: winner.productTitle,
          productSlug: winner.productSlug,
          productImage: winner.productImage,
          winnerId: winner.winnerId,
          winnerEmail: winner.winnerEmail,
          winnerName: winner.winnerName,
          winningBid: winner.winningBid,
          auctionEndDate: winner.auctionEndDate,
          bidId: winner.bidId,
        });

        if (notificationResult.status) {
          console.log(
            `✅ Winner notification sent successfully to: ${winner.winnerEmail} for product: ${winner.productTitle}`
          );
          successfulNotifications.push(winner.winnerEmail);

          // Send auction ended notifications to losing bidders
          try {
            console.log(`📧 Sending auction ended notifications to losing bidders for product: ${winner.productTitle}`);
            await notificationService.sendAuctionEndedNotifications(winner.productId);
            console.log(`✅ Auction ended notifications sent for product: ${winner.productTitle}`);
          } catch (endedNotificationError) {
            console.error(
              `❌ Failed to send auction ended notifications for product ${winner.productId}:`,
              endedNotificationError
            );
          }
        } else {
          console.error(
            `❌ Failed to send winner notification to ${winner.winnerEmail}:`,
            notificationResult.message || 'Unknown error'
          );
          failedNotifications.push(winner.winnerEmail);
        }
      } catch (error) {
        console.error(
          `❌ Error notifying winner ${winner.winnerEmail}:`,
          error instanceof Error ? error.message : error
        );
        failedNotifications.push(winner.winnerEmail);
      }

      // Add delay between notifications to avoid rate limiting
      if (i < winners.length - 1) {
        console.log(`⏳ Waiting 2 seconds before next notification...`);
        await new Promise(resolve => setTimeout(resolve, 2000));
      }
    }

    console.log(
      `📊 Notification summary: ${successfulNotifications.length} successful, ${failedNotifications.length} failed`
    );
    if (failedNotifications.length > 0) {
      console.error(
        `❌ Failed notifications for: ${failedNotifications.join(", ")}`
      );
    }
  }

  /**
   * Force check for ended auctions (for testing/manual trigger)
   */
  async forceCheck(): Promise<any> {
    if (this.isRunning) {
      const error = new Error("Auction check is already running");
      console.warn(
        "⚠️ Force check attempted while scheduler is already running"
      );
      throw error;
    }

    this.isRunning = true;
    console.log("🔄 Force checking ended auctions...");

    try {
      const startTime = Date.now();
      const result = await this.checkEndedAuctions();
      const duration = Date.now() - startTime;

      console.log(`✅ Force check completed in ${duration}ms`);
      return result;
    } catch (error) {
      console.error("❌ Error during force check:", error);
      throw error;
    } finally {
      this.isRunning = false;
    }
  }

  getStatus() {
    return {
      isRunning: this.isRunning,
      checkInterval: this.checkInterval,
      nextCheck: this.getNextCheckTime(),
      lastCheckTime: this.lastCheckTime?.toISOString() || null,
      totalProcessedAuctions: this.totalProcessedAuctions,
      totalNotifiedWinners: this.totalNotifiedWinners,
      lastError: this.lastError,
      uptime: process.uptime(),
      memoryUsage: process.memoryUsage(),
      schedulerVersion: "2.1.0",
    };
  }

  /**
   * Get next scheduled check time with improved calculation
   */
  private getNextCheckTime(): string {
    const now = new Date();
    const nextCheck = new Date(now);

    // Calculate next 2-minute interval
    const currentMinutes = now.getMinutes();
    const nextMinutes = Math.ceil(currentMinutes / 2) * 2;

    if (nextMinutes >= 60) {
      nextCheck.setHours(nextCheck.getHours() + 1);
      nextCheck.setMinutes(0, 0, 0);
    } else {
      nextCheck.setMinutes(nextMinutes, 0, 0);
    }

    return nextCheck.toISOString();
  }

  /**
   * Get detailed statistics about auction processing
   */
  async getAuctionStats() {
    try {
      const [activeAuctions, completedAuctions, totalBids] = await Promise.all([
        prisma.product.count({
          where: {
            sellType: "auction",
            auctionCompleted: false,
            isActive: true,
            status: "active",
          },
        }),
        prisma.product.count({
          where: {
            sellType: "auction",
            auctionCompleted: true,
          },
        }),
        prisma.bid.count(),
      ]);

      return {
        activeAuctions,
        completedAuctions,
        totalBids,
        lastUpdated: new Date().toISOString(),
      };
    } catch (error) {
      console.error("❌ Error getting auction stats:", error);
      return {
        error: "Failed to retrieve auction statistics",
        lastUpdated: new Date().toISOString(),
      };
    }
  }
}

const auctionScheduler = new AuctionScheduler();
export default auctionScheduler;
