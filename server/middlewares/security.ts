import { Context, Next } from "hono";
import crypto from "crypto";

// CSRF token store (in production, use Redis or database)
const csrfTokenStore = new Map<string, { token: string; expires: number }>();

// Rate limiting store for different endpoints
const rateLimitStores = {
  auth: new Map<string, { count: number; resetTime: number }>(),
  api: new Map<string, { count: number; resetTime: number }>(),
  upload: new Map<string, { count: number; resetTime: number }>(),
};

/**
 * Security headers middleware
 */
export const securityHeadersMiddleware = () => {
  return async (c: Context, next: Next) => {
    // Content Security Policy
    c.header(
      "Content-Security-Policy",
      "default-src 'self'; " +
        "script-src 'self' 'unsafe-inline' 'unsafe-eval' https://apis.google.com https://accounts.google.com; " +
        "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; " +
        "font-src 'self' https://fonts.gstatic.com; " +
        "img-src 'self' data: https: blob:; " +
        "connect-src 'self' https://api.exchangerate-api.com https://accounts.google.com ws://localhost:3001 ws://*********:3001 wss://*********:3001; " +
        "frame-src 'self' https://accounts.google.com; " +
        "object-src 'none'; " +
        "base-uri 'self'; " +
        "form-action 'self';"
    );

    // Security headers
    c.header("X-Content-Type-Options", "nosniff");
    c.header("X-Frame-Options", "DENY");
    c.header("X-XSS-Protection", "1; mode=block");
    c.header("Referrer-Policy", "strict-origin-when-cross-origin");
    c.header("Permissions-Policy", "camera=(), microphone=(), geolocation=()");

    // HSTS (HTTP Strict Transport Security)
    if (process.env.NODE_ENV === "production") {
      c.header(
        "Strict-Transport-Security",
        "max-age=********; includeSubDomains; preload"
      );
    }

    // Remove server information
    c.header("Server", "");

    await next();
  };
};

/**
 * CSRF protection middleware
 */
export const csrfProtectionMiddleware = () => {
  return async (c: Context, next: Next) => {
    const method = c.req.method;

    // Skip CSRF for GET, HEAD, OPTIONS requests
    if (["GET", "HEAD", "OPTIONS"].includes(method)) {
      await next();
      return;
    }

    // Skip CSRF for API endpoints with proper authentication
    const path = c.req.path;
    if (path.startsWith("/api/") && c.req.header("Authorization")) {
      await next();
      return;
    }

    const csrfToken =
      c.req.header("X-CSRF-Token") || c.req.header("x-csrf-token");
    const sessionId = c.req.header("X-Session-ID") || "default";

    if (!csrfToken) {
      return c.json({ success: false, message: "CSRF token missing" }, 403);
    }

    const storedData = csrfTokenStore.get(sessionId);
    if (
      !storedData ||
      storedData.token !== csrfToken ||
      Date.now() > storedData.expires
    ) {
      return c.json(
        { success: false, message: "Invalid or expired CSRF token" },
        403
      );
    }

    await next();
  };
};

/**
 * Generate CSRF token
 */
export const generateCSRFToken = (sessionId: string = "default"): string => {
  const token = crypto.randomBytes(32).toString("hex");
  const expires = Date.now() + 60 * 60 * 1000; // 1 hour

  csrfTokenStore.set(sessionId, { token, expires });

  // Clean up expired tokens
  for (const [id, data] of csrfTokenStore.entries()) {
    if (Date.now() > data.expires) {
      csrfTokenStore.delete(id);
    }
  }

  return token;
};

export const enhancedRateLimitMiddleware = (
  type: "auth" | "api" | "upload" = "api",
  maxRequests: number = 100,
  windowMs: number = 15 * 60 * 1000,
  skipSuccessfulRequests: boolean = false
) => {
  return async (c: Context, next: Next) => {
    const store = rateLimitStores[type];
    const clientIp =
      c.req.header("x-forwarded-for") || c.req.header("x-real-ip") || "unknown";
    const key = `${type}_${clientIp}`;
    const now = Date.now();

    const current = store.get(key);

    if (!current || now > current.resetTime) {
      store.set(key, { count: 1, resetTime: now + windowMs });
    } else {
      if (current.count >= maxRequests) {
        // Add rate limit headers
        c.header("X-RateLimit-Limit", maxRequests.toString());
        c.header("X-RateLimit-Remaining", "0");
        c.header(
          "X-RateLimit-Reset",
          Math.ceil(current.resetTime / 1000).toString()
        );

        return c.json(
          {
            success: false,
            message: "Too many requests. Please try again later.",
            retryAfter: Math.ceil((current.resetTime - now) / 1000),
          },
          429
        );
      }

      current.count++;
    }

    // Add rate limit headers
    const remaining = Math.max(0, maxRequests - (current?.count || 1));
    c.header("X-RateLimit-Limit", maxRequests.toString());
    c.header("X-RateLimit-Remaining", remaining.toString());
    c.header(
      "X-RateLimit-Reset",
      Math.ceil((current?.resetTime || now + windowMs) / 1000).toString()
    );

    await next();

    // If skipSuccessfulRequests is true and request was successful, decrement counter
    if (skipSuccessfulRequests && c.res.status < 400) {
      const currentData = store.get(key);
      if (currentData && currentData.count > 0) {
        currentData.count--;
      }
    }
  };
};

/**
 * Request size limiting middleware
 */
export const requestSizeLimitMiddleware = (
  maxSize: number = 10 * 1024 * 1024
) => {
  // 10MB default
  return async (c: Context, next: Next) => {
    const contentLength = c.req.header("content-length");

    if (contentLength && parseInt(contentLength) > maxSize) {
      return c.json(
        {
          success: false,
          message: `Request too large. Maximum size is ${Math.round(maxSize / 1024 / 1024)}MB`,
        },
        413
      );
    }

    await next();
  };
};

/**
 * IP whitelist/blacklist middleware
 */
export const ipFilterMiddleware = (
  whitelist: string[] = [],
  blacklist: string[] = []
) => {
  return async (c: Context, next: Next) => {
    const clientIp =
      c.req.header("x-forwarded-for") ||
      c.req.header("x-real-ip") ||
      c.req.header("cf-connecting-ip") ||
      "unknown";

    // Check blacklist first
    if (blacklist.length > 0 && blacklist.includes(clientIp)) {
      return c.json({ success: false, message: "Access denied" }, 403);
    }

    // Check whitelist if configured
    if (whitelist.length > 0 && !whitelist.includes(clientIp)) {
      return c.json({ success: false, message: "Access denied" }, 403);
    }

    await next();
  };
};

/**
 * Request logging middleware for security monitoring
 */
export const securityLoggingMiddleware = () => {
  return async (c: Context, next: Next) => {
    const start = Date.now();
    const clientIp =
      c.req.header("x-forwarded-for") || c.req.header("x-real-ip") || "unknown";
    const userAgent = c.req.header("user-agent") || "unknown";
    const method = c.req.method;
    const path = c.req.path;

    await next();

    const duration = Date.now() - start;
    const status = c.res.status;

    // Log suspicious activities
    if (status === 401 || status === 403 || status === 429) {
      console.warn(
        `🚨 Security Alert: ${method} ${path} - Status: ${status} - IP: ${clientIp} - UA: ${userAgent} - Duration: ${duration}ms`
      );
    }

    // Log slow requests (potential DoS)
    if (duration > 5000) {
      console.warn(
        `⚠️ Slow Request: ${method} ${path} - Duration: ${duration}ms - IP: ${clientIp}`
      );
    }
  };
};

/**
 * Clean up expired data periodically
 */
setInterval(() => {
  // Clean up CSRF tokens
  for (const [id, data] of csrfTokenStore.entries()) {
    if (Date.now() > data.expires) {
      csrfTokenStore.delete(id);
    }
  }

  // Clean up rate limit stores
  Object.values(rateLimitStores).forEach((store) => {
    for (const [key, data] of store.entries()) {
      if (Date.now() > data.resetTime) {
        store.delete(key);
      }
    }
  });
}, 60000); // Clean up every minute
