'use client';

import { useSession as useNextAuthSession, signIn, signOut } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { useCallback, useMemo } from 'react';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import axios from 'axios';

// Types
export interface User {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  phoneNumber: string;
}

export interface SessionData {
  user: User | null;
  accessToken: string | null;
  refreshToken: string | null;
  isLoading: boolean;
  isAuthenticated: boolean;
}

// Profile API query
const useProfileQuery = (accessToken: string | null) => {
  return useQuery({
    queryKey: ['profile', accessToken],
    queryFn: async (): Promise<User> => {
      if (!accessToken) throw new Error('No access token');
      
      const response = await axios.get(`${process.env.NEXT_PUBLIC_API_URL}/auth/profile`, {
        headers: {
          Authorization: `Bearer ${accessToken}`
        }
      });

      if (!response.data.status) {
        throw new Error(response.data.message || 'Failed to fetch profile');
      }

      return response?.data;
    },
    enabled: !!accessToken,
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 1,
    refetchOnWindowFocus: false,
  });
};

export function useSession(): SessionData & {
  login: (email: string, password: string) => Promise<{ success: boolean; error?: string }>;
  loginWithGoogle: () => Promise<void>;
  logout: () => Promise<void>;
  refreshProfile: () => Promise<void>;
} {
  const { data: session, status, update } = useNextAuthSession();
  const router = useRouter();
  const queryClient = useQueryClient();

  const accessToken = session?.accessToken || null;
  const refreshToken = session?.refreshToken || null;

  // Fetch profile data using React Query
  const { data: profileData, isLoading: isProfileLoading, refetch: refetchProfile } = useProfileQuery(accessToken);

  // Memoized user data - prefer profile data over session data
  const user = useMemo(() => {
    if (profileData) {
      return profileData;
    }
    
    if (session?.user) {
      return {
        id: session.user.id,
        firstName: session.user.firstName,
        lastName: session.user.lastName,
        email: session.user.email || '',
        phoneNumber: session.user.phoneNumber,
      };
    }
    
    return null;
  }, [profileData, session?.user]);

  const isLoading = status === 'loading' || isProfileLoading;
  const isAuthenticated = status === 'authenticated' && !!session && !!user;

  // Login with credentials
  const login = useCallback(async (email: string, password: string): Promise<{ success: boolean; error?: string }> => {
    try {
      const result = await signIn('credentials', {
        email,
        password,
        redirect: false,
      });

      if (result?.error) {
        return { success: false, error: result.error };
      }

      if (result?.ok) {
        // Invalidate profile query to refetch fresh data
        queryClient.invalidateQueries({ queryKey: ['profile'] });
        router.push('/');
        return { success: true };
      }

      return { success: false, error: 'Login failed' };
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Login failed';
      return { success: false, error: errorMessage };
    }
  }, [router, queryClient]);

  // Login with Google
  const loginWithGoogle = useCallback(async (): Promise<void> => {
    try {
      await signIn('google', { 
        callbackUrl: '/',
        redirect: true 
      });
    } catch (err) {
      console.error('Google login failed:', err);
      throw err;
    }
  }, []);

  // Logout
  const logout = useCallback(async (): Promise<void> => {
    try {
      // Clear React Query cache
      queryClient.clear();
      
      // Sign out from NextAuth
      await signOut({ 
        callbackUrl: '/auth/login',
        redirect: true 
      });
    } catch (err) {
      console.error('Logout failed:', err);
      throw err;
    }
  }, [queryClient]);

  // Refresh profile data
  const refreshProfile = useCallback(async (): Promise<void> => {
    try {
      // Update NextAuth session
      await update();
      // Refetch profile data
      await refetchProfile();
    } catch (err) {
      console.error('Failed to refresh profile:', err);
      throw err;
    }
  }, [update, refetchProfile]);

  return {
    user,
    accessToken,
    refreshToken,
    isLoading,
    isAuthenticated,
    login,
    loginWithGoogle,
    logout,
    refreshProfile,
  };
}

// Hook for getting access token only
export function useAccessToken(): string | null {
  const { data: session } = useNextAuthSession();
  return session?.accessToken || null;
}

// Hook for checking authentication status
export function useAuth(): { isAuthenticated: boolean; isLoading: boolean } {
  const { status } = useNextAuthSession();
  return {
    isAuthenticated: status === 'authenticated',
    isLoading: status === 'loading',
  };
}
