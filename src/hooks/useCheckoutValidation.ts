import { useQuery } from "@tanstack/react-query";
import { useAuthenticatedApi } from "@/services/useAuthQuery";
import { useSession } from "next-auth/react";
import { 
  CheckoutType, 
  CheckoutContext, 
  CheckoutValidationResult,
  CheckoutError 
} from "@/services/useCheckoutQuery";

interface UseCheckoutValidationParams {
  checkoutType: CheckoutType;
  productId?: string;
  bidId?: string;
  enabled?: boolean;
}

interface CheckoutValidationResponse {
  data?: CheckoutContext;
  isLoading: boolean;
  error: CheckoutError | null;
  isValid: boolean;
  refetch: () => void;
}

export const useCheckoutValidation = ({
  checkoutType,
  productId,
  bidId,
  enabled = true
}: UseCheckoutValidationParams): CheckoutValidationResponse => {
  const api = useAuthenticatedApi();
  const { data: session, status: sessionStatus } = useSession();

  const {
    data: validationResult,
    isLoading,
    error: queryError,
    refetch
  } = useQuery({
    queryKey: ['checkout-validation', checkoutType, productId, bidId],
    queryFn: async (): Promise<CheckoutValidationResult> => {
      console.log('Validation Query Starting:', { checkoutType, productId, bidId, api: !!api, session: !!session?.user?.id });

      if (!api) {
        throw new Error('Authentication required');
      }

      try {
        const response: CheckoutValidationResult = await api.post('/checkout/validate', {
          checkoutType,
          productId,
          bidId
        });

        console.log('Validation API Response:', response.data);
        return response;
      } catch (error: any) {
        console.log('Validation API Error:', error.response?.data || error.message);
        if (error.response?.data) {
          throw error;
        }
        throw new Error('Network error occurred');
      }
    },
    enabled: enabled && !!api && !!session?.user?.id,
    staleTime: 30 * 1000, // 30 seconds
    retry: (failureCount, error: any) => {
      if (error?.response?.status >= 400 && error?.response?.status < 500) {
        return false;
      }
      return failureCount < 2;
    }
  });

  const transformError = (error: any): CheckoutError => {
    if (!error) return { type: 'unknown', message: 'Unknown error occurred' };

    if (error.response?.data?.message) {
      const status = error.response.status;
      let type: CheckoutError['type'] = 'unknown';
      
      if (status === 400 || status === 422) {
        type = 'validation';
      } else if (status === 402 || status === 403) {
        type = 'payment';
      } else if (status >= 500) {
        type = 'network';
      }

      return {
        type,
        message: error.response.data.message,
        details: error.response.data.data
      };
    }

    if (error.message?.includes('network') || error.message?.includes('fetch')) {
      return {
        type: 'network',
        message: 'Network error. Please check your connection and try again.'
      };
    }

    return {
      type: 'unknown',
      message: error.message || 'An unexpected error occurred'
    };
  };

  const result = {
    data: validationResult?.data?.context,
    isLoading,
    error: queryError ? transformError(queryError) : null,
    isValid: validationResult?.status === true,
    refetch
  };

  return result;
};

/**
 * Hook for validating cart checkout
 */
export const useCartCheckoutValidation = (enabled = true) => {
  return useCheckoutValidation({
    checkoutType: 'cart',
    enabled
  });
};

/**
 * Hook for validating buy-now checkout
 */
export const useBuyNowCheckoutValidation = (productId: string, enabled = true) => {
  return useCheckoutValidation({
    checkoutType: 'buy-now',
    productId,
    enabled: enabled && !!productId
  });
};

/**
 * Hook for validating bidding checkout
 */
export const useBiddingCheckoutValidation = (
  productId: string, 
  bidId?: string, 
  enabled = true
) => {
  return useCheckoutValidation({
    checkoutType: 'bidding',
    productId,
    bidId,
    enabled: enabled && !!productId
  });
};

/**
 * Utility function to get user-friendly error messages
 */
export const getCheckoutErrorMessage = (error: CheckoutError): string => {
  switch (error.type) {
    case 'validation':
      return error.message || 'Please check your information and try again.';
    case 'payment':
      return 'Payment method issue. Please update your payment information.';
    case 'inventory':
      return 'This item is no longer available. Please try a different item.';
    case 'network':
      return 'Connection issue. Please check your internet and try again.';
    default:
      return 'Something went wrong. Please try again later.';
  }
};

/**
 * Utility function to determine if error is recoverable
 */
export const isRecoverableError = (error: CheckoutError): boolean => {
  return error.type === 'network' || error.type === 'unknown';
};
