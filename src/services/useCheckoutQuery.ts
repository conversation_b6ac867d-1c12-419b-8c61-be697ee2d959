import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { useAuthenticatedApi } from "./useAuthQuery";
import { toaster } from "@/components/ui/toaster";

export type CheckoutType = 'cart' | 'buy-now' | 'bidding';

export interface CheckoutValidationRequest {
  checkoutType: CheckoutType
  productId?: string
  bidId?: string
}

export interface CheckoutValidationResponse {
  status: boolean
  message: string
  data?: any
}

export interface ShippingAddress {
  id?: string;
  name: string;
  country: string;
  province: string;
  city: string;
  postalCode: string;
  fullAddress: string;
  isDefault?: boolean;
  createdAt?: string;
  updatedAt?: string;
}

export interface CheckoutFormData {
  // Shipping Information
  shippingAddressId?: string;
  name: string;
  address: string;
  city: string;
  provinceRegion: string;
  zipCode: string;
  country: string;

  // Payment Information
  paymentMethod: string;
  selectedPaymentMethod?: string;
  paymentChannel?: string;
  ewalletType?: string;
  bankCode?: string;
  retailOutletName?: string;

  // Shipping Options
  shippingOption?: {
    id: string;
    name: string;
    cost: number;
    estimatedDays: number;
  };

  // Currency and Additional Information
  currency: 'USD' | 'IDR';
  notes?: string;
}

// Checkout context interfaces for different types
export interface CartCheckoutContext {
  type: 'cart';
  items: Array<{
    id: string;
    productId: string;
    quantity: number;
    price: number;
    product: {
      id: string;
      itemName: string;
      slug: string;
      priceUSD: number;
      images: Array<{ id: string; imageUrl: string; isMain: boolean; }>;
      sellType: 'buy-now';
      status: string;
    };
  }>;
  totalItems: number;
  totalPrice: number;
}

export interface BuyNowCheckoutContext {
  type: 'buy-now';
  productId: string;
  product: {
    id: string;
    itemName: string;
    slug: string;
    priceUSD: number;
    images: Array<{ id: string; imageUrl: string; isMain: boolean; }>;
    sellType: 'buy-now';
    status: string;
  };
  quantity: number;
}

export interface BiddingCheckoutContext {
  type: 'bidding';
  productId: string;
  bidId?: string;
  product: {
    id: string;
    itemName: string;
    slug: string;
    priceUSD: number;
    images: Array<{ id: string; imageUrl: string; isMain: boolean; }>;
    sellType: 'auction';
    status: string;
    auctionEndDate: string;
    auctionCompleted: boolean;
    winnerId?: string;
    winnerBidId?: string;
  };
  winningBid: {
    id: string;
    amount: number;
    bidderId: string;
    bidder?: {
      id: string;
      firstName: string;
      lastName: string;
    };
  };
  paymentDeadline: string;
  hoursRemaining: number;
  auctionEndDate: string;
}

export type CheckoutContext = CartCheckoutContext | BuyNowCheckoutContext | BiddingCheckoutContext;

export interface PaymentMethod {
  id?: string;
  type: string;
  method?: string;
  channel?: string;
  cardNumber?: string;
  cardHolderName?: string;
  expiryMonth?: string;
  expiryYear?: string;
  cvv?: string;
  paypalEmail?: string;
  bankAccountNumber?: string;
  bankName?: string;
  ewalletType?: string;
  bankCode?: string;
  retailOutletName?: string;
  isDefault?: boolean;
}

export interface OrderItem {
  productId: string;
  quantity: number;
  price: number;
  currency: 'USD' | 'IDR';
  bidId?: string; // For auction orders
  product: {
    id: string;
    itemName: string;
    slug?: string;
    priceUSD: number;
    sellType: 'buy-now' | 'auction';
    images: Array<{
      id: string;
      imageUrl: string;
      isMain: boolean;
    }>;
  };
  bid?: {
    id: string;
    amount: number;
    bidderId: string;
  };
}

// Base order data interface
interface BaseOrderData {
  currency: 'USD' | 'IDR';
  shippingAddress?: ShippingAddress;
  shippingAddressId?: string;
  notes?: string;
  paymentMethod: PaymentMethod;
}

// Cart checkout order data
export interface CartOrderData extends BaseOrderData {
  orderType: 'cart';
  // Cart items are automatically fetched from user's cart
}

// Buy now checkout order data
export interface BuyNowOrderData extends BaseOrderData {
  orderType: 'buy-now';
  productId: string;
  quantity?: number;
}

// Bidding checkout order data
export interface BiddingOrderData extends BaseOrderData {
  orderType: 'bidding';
  productId: string;
  bidId: string;
  winningBid: number;
}

// Union type for all order data types
export type CreateOrderData = CartOrderData | BuyNowOrderData | BiddingOrderData;

// Legacy interface for backward compatibility
export interface LegacyCreateOrderData {
  items: Array<{
    productId: string;
    quantity: number;
  }>;
  shippingAddress: ShippingAddress;
  paymentMethod: PaymentMethod;
  notes?: string;
}

export interface LegacyBuyNowData {
  products: string[];
  shippingAddress: ShippingAddress;
  paymentMethod: PaymentMethod;
  notes?: string;
}

export interface Order {
  id: string;
  userId: string;
  orderNumber: string;
  status: 'pending' | 'pending_payment' | 'confirmed' | 'processing' | 'shipped' | 'delivered' | 'cancelled';
  orderType: 'cart' | 'buy-now' | 'bidding';
  items: OrderItem[];
  subtotal: number;
  shippingCost: number;
  tax: number;
  total: number;
  currency: 'USD' | 'IDR';
  shippingAddress: ShippingAddress;
  paymentMethod: string;
  paymentStatus: 'pending' | 'paid' | 'failed' | 'refunded';
  bidId?: string; // For auction orders
  winningBid?: number; // For auction orders
  notes?: string;
  createdAt: string;
  updatedAt: string;
}

// API Response types
export interface CheckoutValidationResult {
  status: boolean;
  message: string;
  data?: {
    checkoutType: CheckoutType;
    context: CheckoutContext;
  };
}

export interface OrderCreationResult {
  status: boolean;
  message: string;
  data?: Order;
}

// Checkout error types
export interface CheckoutError {
  type: 'validation' | 'payment' | 'inventory' | 'network' | 'unknown';
  message: string;
  details?: Record<string, any>;
}

export interface CheckoutSession {
  id: string;
  items: OrderItem[];
  subtotal: number;
  shippingCost: number;
  tax: number;
  total: number;
  expiresAt: string;
}

// Query Keys
export const checkoutQueryKeys = {
  all: ['checkout'] as const,
  session: (sessionId: string) => [...checkoutQueryKeys.all, 'session', sessionId] as const,
  orders: () => [...checkoutQueryKeys.all, 'orders'] as const,
  order: (orderId: string) => [...checkoutQueryKeys.orders(), orderId] as const,
  addresses: () => [...checkoutQueryKeys.all, 'addresses'] as const,
  paymentMethods: () => [...checkoutQueryKeys.all, 'payment-methods'] as const,
};

// Create Checkout Session (from Cart)
export const useCreateCheckoutSessionMutation = () => {
  const apiClient = useAuthenticatedApi();

  return useMutation({
    mutationFn: async (): Promise<CheckoutSession> => {
      const response = await apiClient.post('/checkout/session');
      return response.data;
    },
    onError: (error) => {
      toaster.create({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to create checkout session",
        type: "error",
      });
    },
  });
};

// Unified Checkout Order Creation Mutation
export const useUnifiedCheckoutMutation = () => {
  const apiClient = useAuthenticatedApi();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: {
      checkoutType: CheckoutType;
      formData: CheckoutFormData;
      checkoutContext: CheckoutContext;
    }): Promise<Order> => {
      const { checkoutType, formData, checkoutContext } = data;

      // Transform form data to API format based on checkout type
      const orderData = {
        orderType: checkoutType,
        currency: formData.currency,
        notes: formData.notes,
        // Handle shipping address
        ...(formData.shippingAddressId
          ? { shippingAddressId: formData.shippingAddressId }
          : {
              shippingAddress: {
                name: formData.name,
                fullAddress: formData.address,
                city: formData.city,
                province: formData.provinceRegion,
                postalCode: formData.zipCode,
                country: formData.country,
              }
            }
        ),
        // Handle payment method
        paymentMethod: {
          type: formData.paymentMethod,
          method: formData.selectedPaymentMethod,
          channel: formData.paymentChannel,
          ewalletType: formData.ewalletType,
          bankCode: formData.bankCode,
          retailOutletName: formData.retailOutletName,
        },
        // Add type-specific data
        ...(checkoutType === 'buy-now' && checkoutContext.type === 'buy-now' && {
          productId: checkoutContext.productId,
          quantity: checkoutContext.quantity,
        }),
        ...(checkoutType === 'bidding' && checkoutContext.type === 'bidding' && {
          productId: checkoutContext.productId,
          bidId: checkoutContext.bidId,
        }),
      };

      const response = await apiClient.post('/checkout/orders', orderData);

      if (!response.data?.status || !response.data?.data) {
        throw new Error(response.data?.message || 'Failed to create order');
      }

      return response.data.data;
    },
    onSuccess: (order) => {
      // Invalidate related queries
      queryClient.invalidateQueries({ queryKey: checkoutQueryKeys.orders() });
      queryClient.invalidateQueries({ queryKey: ['cart'] });

      toaster.create({
        title: "Order Created Successfully",
        description: `Order #${order.orderNumber} has been created. Redirecting to payment...`,
        type: "success",
      });
    },
    onError: (error: any) => {
      console.error('Checkout error:', error);

      const errorMessage = error?.response?.data?.message ||
                          error?.message ||
                          'Failed to create order. Please try again.';

      toaster.create({
        title: "Checkout Failed",
        description: errorMessage,
        type: "error",
      });
    },
  });
};

// Create Order from Checkout Session (Legacy - kept for backward compatibility)
export const useCreateOrderMutation = () => {
  const apiClient = useAuthenticatedApi();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: CreateOrderData): Promise<Order> => {
      const response = await apiClient.post('/orders', data);
      return response.data;
    },
    onSuccess: (data) => {
      // Invalidate related queries
      queryClient.invalidateQueries({ queryKey: checkoutQueryKeys.orders() });
      queryClient.invalidateQueries({ queryKey: ['cart'] });

      toaster.create({
        title: "Order Created",
        description: `Order #${data.orderNumber} has been created successfully.`,
        type: "success",
      });
    },
    onError: (error) => {
      toaster.create({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to create order",
        type: "error",
      });
    },
  });
};



// Get User Orders
export const useOrdersQuery = () => {
  const apiClient = useAuthenticatedApi();

  return useQuery({
    queryKey: checkoutQueryKeys.orders(),
    queryFn: async (): Promise<Order[]> => {
      const response = await apiClient.get('/orders');
      return response.data;
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

// Get Single Order
export const useOrderQuery = (orderId: string) => {
  const apiClient = useAuthenticatedApi();

  return useQuery({
    queryKey: checkoutQueryKeys.order(orderId),
    queryFn: async (): Promise<Order> => {
      const response = await apiClient.get(`/orders/${orderId}`);
      return response.data;
    },
    enabled: !!orderId,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

// Get Shipping Addresses
export const useShippingAddressesQuery = () => {
  const apiClient = useAuthenticatedApi();

  return useQuery({
    queryKey: checkoutQueryKeys.addresses(),
    queryFn: async (): Promise<ShippingAddress[]> => {
      const response = await apiClient.get('/user/addresses');
      if (response.status && response.data) {
        return response.data;
      }
      return [];
    },
    staleTime: 10 * 60 * 1000, // 10 minutes
  });
};

// Create Shipping Address
export const useCreateShippingAddressMutation = () => {
  const apiClient = useAuthenticatedApi();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: ShippingAddress): Promise<ShippingAddress> => {
      const response = await apiClient.post('/user/addresses', data);
      if (response.status && response.data) {
        return response.data;
      }
      throw new Error('Failed to create address');
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: checkoutQueryKeys.addresses() });

      toaster.create({
        title: "Address Added",
        description: "Shipping address has been added successfully.",
        type: "success",
      });
    },
    onError: (error) => {
      toaster.create({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to add shipping address",
        type: "error",
      });
    },
  });
};

// Get Payment Methods
export const usePaymentMethodsQuery = () => {
  const apiClient = useAuthenticatedApi();

  return useQuery({
    queryKey: checkoutQueryKeys.paymentMethods(),
    queryFn: async (): Promise<PaymentMethod[]> => {
      const response = await apiClient.get('/user/payment-methods');
      return response.data;
    },
    staleTime: 10 * 60 * 1000, // 10 minutes
  });
};

// Create Payment Method
export const useCreatePaymentMethodMutation = () => {
  const apiClient = useAuthenticatedApi();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: PaymentMethod): Promise<PaymentMethod> => {
      const response = await apiClient.post('/user/payment-methods', data);
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: checkoutQueryKeys.paymentMethods() });
      
      toaster.create({
        title: "Payment Method Added",
        description: "Payment method has been added successfully.",
        type: "success",
      });
    },
    onError: (error) => {
      toaster.create({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to add payment method",
        type: "error",
      });
    },
  });
};

// Enhanced Buy Now Mutation (matches working reference code)
export const useBuyNowMutation = () => {
  const apiClient = useAuthenticatedApi();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (orderData: any): Promise<any> => {
      let endpoint = '/checkout/orders';

      // Use specific endpoints for better performance and clearer separation
      if (orderData.orderType === 'cart') {
        endpoint = '/checkout/orders'; // Cart checkout endpoint
        console.log('[CHECKOUT] Using cart checkout endpoint');
      } else if (orderData.orderType === 'buy-now') {
        endpoint = '/checkout/orders/buy-now'; // Buy-now checkout endpoint
        console.log('[CHECKOUT] Using buy-now checkout endpoint');
      } else if (orderData.orderType === 'bidding') {
        endpoint = '/checkout/orders'; // Unified endpoint for bidding
        console.log('[CHECKOUT] Using bidding checkout endpoint');
      }

      console.log('[CHECKOUT] Sending order data:', {
        orderType: orderData.orderType,
        endpoint,
        hasProducts: !!orderData.products,
        hasProductId: !!orderData.productId,
        hasBidId: !!orderData.bidId
      });

      const response = await apiClient.post(endpoint, orderData);

      if (!response.data?.status || !response.data?.data) {
        throw new Error(response.data?.message || 'Failed to create order');
      }

      console.log('[CHECKOUT] Order created successfully:', response.data.data.orderNumber);
      return response.data.data;
    },
    onSuccess: (order, variables) => {
      console.log('[CHECKOUT] Order created successfully:', order.orderNumber);

      // Immediately clear cart data from cache for cart checkouts
      if (variables.orderType === 'cart') {
        queryClient.setQueryData(['cart'], (oldData: any) => {
          if (oldData) {
            return {
              ...oldData,
              items: [],
              totalItems: 0,
              totalPrice: 0
            };
          }
          return oldData;
        });
      }

      // Invalidate and refetch relevant queries
      queryClient.invalidateQueries({ queryKey: ['cart'] });
      queryClient.invalidateQueries({ queryKey: ['orders'] });

      // Force refetch cart data to ensure UI is updated
      queryClient.refetchQueries({ queryKey: ['cart'] });
    },
    onError: (error: any) => {
      console.error('Order creation error:', error);
      // Let the checkout page handle error display to avoid double toasters
    },
  });
};

// Enhanced Payment Creation Mutations (matches working reference code)
export const useCreateInvoiceMutation = () => {
  const apiClient = useAuthenticatedApi();

  return useMutation({
    mutationFn: async (data: {
      orderId: string;
      currency: string;
      customerEmail: string;
      customerName: string;
      description: string;
      successRedirectUrl: string;
      failureRedirectUrl: string;
    }): Promise<{
      id: string;
      invoiceUrl: string;
      expiresAt: string;
    }> => {
      const response = await apiClient.post('/payments/invoice', data);

      if (!response.data?.status || !response.data?.data) {
        throw new Error(response.data?.message || 'Failed to create invoice');
      }

      return response.data.data;
    },
    onError: (error: any) => {
      console.error('Invoice creation error:', error);
      // Let the checkout page handle error display to avoid double toasters
    },
  });
};

// Payment Creation Mutation
export const useCreatePaymentMutation = () => {
  const apiClient = useAuthenticatedApi();

  return useMutation({
    mutationFn: async (data: {
      orderId: string;
      paymentType: 'invoice' | 'ewallet' | 'virtual_account' | 'retail_outlet' | 'credit_card';
      paymentMethod?: string;
      ewalletType?: string;
      bankCode?: string;
      retailOutletName?: string;
      customerName?: string;
      customerEmail?: string;
      customerPhone?: string;
    }): Promise<{
      paymentId: string;
      paymentUrl?: string;
      qrCode?: string;
      virtualAccountNumber?: string;
      retailCode?: string;
      expiresAt: string;
      actions?: any[];
    }> => {
      let endpoint = '/payments/create';
      let paymentData: any = { ...data };

      // Use specific endpoints based on payment type for better handling
      switch (data.paymentType) {
        case 'invoice':
          endpoint = '/payments/invoice';
          paymentData = {
            orderId: data.orderId,
            customerName: data.customerName || 'Customer',
            customerEmail: data.customerEmail || '<EMAIL>',
            description: `Payment for order`,
            successRedirectUrl: `${window.location.origin}/orders/${data.orderId}?payment=success`,
            failureRedirectUrl: `${window.location.origin}/orders/${data.orderId}?payment=failed`,
          };
          break;
        case 'ewallet':
          endpoint = '/payments/ewallet';
          paymentData = {
            orderId: data.orderId,
            ewalletType: data.ewalletType,
            customerName: data.customerName || 'Customer',
            customerPhone: data.customerPhone || '+*************',
          };
          break;
        case 'virtual_account':
          endpoint = '/payments/virtual-account';
          paymentData = {
            orderId: data.orderId,
            bankCode: data.bankCode,
            customerName: data.customerName || 'Customer',
          };
          break;
        case 'retail_outlet':
          endpoint = '/payments/retail-outlet';
          paymentData = {
            orderId: data.orderId,
            retailOutletName: data.retailOutletName,
            customerName: data.customerName || 'Customer',
          };
          break;
        default:
          // Fallback to general create endpoint
          break;
      }

      const response = await apiClient.post(endpoint, paymentData);

      if (!response.data?.status || !response.data?.data) {
        throw new Error(response.data?.message || 'Failed to create payment');
      }

      const paymentResult = response.data.data;

      return {
        paymentId: paymentResult.id || paymentResult.paymentId,
        paymentUrl: paymentResult.invoice_url || paymentResult.paymentUrl,
        qrCode: paymentResult.qr_string || paymentResult.qrCode,
        virtualAccountNumber: paymentResult.account_number || paymentResult.virtualAccountNumber,
        retailCode: paymentResult.payment_code || paymentResult.retailCode,
        expiresAt: paymentResult.expiry_date || paymentResult.expiresAt || new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
        actions: paymentResult.actions,
      };
    },
    onError: (error: any) => {
      console.error('Payment creation error:', error);
      // Let the checkout page handle error display to avoid double toasters
    },
  });
};

// Checkout validation hook
export const useCheckoutValidationMutation = () => {
  const apiClient = useAuthenticatedApi();

  return useMutation({
    mutationFn: async (data: CheckoutValidationRequest): Promise<any> => {
      const response = await apiClient.post('/checkout/validate', data);
      return response;
    },
  });
};
