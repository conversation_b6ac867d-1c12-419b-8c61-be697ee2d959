import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import { useAuthenticatedApi } from './useAuthQuery'
import { toaster } from '@/components/ui/toaster'

// Types
export interface ProfileData {
  id: string
  firstName: string
  lastName: string
  email: string
  phoneNumber: string
  sellerAddressName?: string
  sellerCountry?: string
  sellerProvince?: string
  sellerCity?: string
  sellerPostalCode?: string
  sellerFullAddress?: string
  sellerAddressComplete?: boolean
  emailVerified: boolean
  createdAt: string
  updatedAt: string
}

export interface UpdateProfileData {
  firstName: string
  lastName: string
  phoneNumber: string
  sellerAddressName?: string
  sellerCountry?: string
  sellerProvince?: string
  sellerCity?: string
  sellerPostalCode?: string
  sellerFullAddress?: string
}

export interface VerificationRequest {
  type: 'email'
  value: string
}

export interface VerificationConfirm {
  type: 'email'
  code: string
}

// Query Keys
export const profileQueryKeys = {
  all: ['profile'] as const,
  profile: () => [...profileQueryKeys.all, 'data'] as const,
  verification: () => [...profileQueryKeys.all, 'verification'] as const,
}

// Get Profile
export const useProfileQuery = () => {
  const apiClient = useAuthenticatedApi()

  return useQuery({
    queryKey: profileQueryKeys.profile(),
    queryFn: async (): Promise<ProfileData> => {
      const response = await apiClient.get('/auth/profile')
      console.log('Profile data fetched:', response)

      return response?.data
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 1,
  })
}

// Update Profile
export const useUpdateProfileMutation = () => {
  const apiClient = useAuthenticatedApi()
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async (data: UpdateProfileData): Promise<ProfileData> => {
      const response = await apiClient.put('/user/profile', data)
      return response.data.data
    },
    onSuccess: (data) => {
      queryClient.setQueryData(profileQueryKeys.profile(), data)
      queryClient.invalidateQueries({ queryKey: profileQueryKeys.all })
      
      toaster.create({
        title: "Profile Updated",
        description: "Your profile has been updated successfully.",
        type: "success",
      })
    },
    onError: (error) => {
      toaster.create({
        title: "Update Failed",
        description: error instanceof Error ? error.message : "Failed to update profile",
        type: "error",
      })
    },
  })
}

// Request Verification
export const useRequestVerificationMutation = () => {
  const apiClient = useAuthenticatedApi()

  return useMutation({
    mutationFn: async (data: VerificationRequest): Promise<{ message: string }> => {
      const response = await apiClient.post('/user/verification/request', data)
      return response.data
    },
    onSuccess: (data, variables) => {
      toaster.create({
        title: "Verification Sent",
        description: `Verification code sent to your ${variables.type}`,
        type: "success",
      })
    },
    onError: (error) => {
      toaster.create({
        title: "Verification Failed",
        description: error instanceof Error ? error.message : "Failed to send verification",
        type: "error",
      })
    },
  })
}

// Confirm Verification
export const useConfirmVerificationMutation = () => {
  const apiClient = useAuthenticatedApi()
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async (data: VerificationConfirm): Promise<{ message: string }> => {
      const response = await apiClient.post('/user/verification/confirm', data)
      return response.data
    },
    onSuccess: (data, variables) => {
      queryClient.invalidateQueries({ queryKey: profileQueryKeys.all })
      
      toaster.create({
        title: "Verification Complete",
        description: `Your ${variables.type} has been verified successfully`,
        type: "success",
      })
    },
    onError: (error) => {
      toaster.create({
        title: "Verification Failed",
        description: error instanceof Error ? error.message : "Invalid verification code",
        type: "error",
      })
    },
  })
}

// Check Seller Status
export const useSellerStatusQuery = () => {
  const apiClient = useAuthenticatedApi()

  return useQuery({
    queryKey: [...profileQueryKeys.all, 'seller-status'],
    queryFn: async (): Promise<{
      canSell: boolean
      emailVerified: boolean
      phoneVerified: boolean
      missingRequirements: string[]
    }> => {
      const response = await apiClient.get('/user/seller-status')
      return response?.data
    },
    staleTime: 2 * 60 * 1000, // 2 minutes
  })
}
