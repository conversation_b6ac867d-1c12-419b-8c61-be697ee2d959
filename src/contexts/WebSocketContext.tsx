'use client'

import React, { createContext, useContext, useEffect, useRef, useState, useCallback, ReactNode } from 'react'
import { useQueryClient } from '@tanstack/react-query'
import { useSession } from 'next-auth/react'
import { wsDebugger } from '@/utils/websocket-debug'
import { toaster } from '@/components/ui/toaster'

interface WebSocketMessage {
  type: string
  channel?: string
  data: any
  timestamp: string
}

interface WebSocketContextType {
  isConnected: boolean
  connectionStatus: 'connecting' | 'connected' | 'disconnected' | 'error'
  subscribe: (channel: string, callback?: (data: any) => void) => void
  unsubscribe: (channel: string, callback?: (data: any) => void) => void
  sendMessage: (message: any) => void
  lastMessage: WebSocketMessage | null
  connectionStats: {
    reconnectAttempts: number
    messagesReceived: number
    messagesSent: number
    uptime: number
    latency: number
    connectionQuality: 'excellent' | 'good' | 'poor' | 'unknown'
  }
  reconnect: () => void
  disconnect: () => void
  // Enhanced methods for better stability
  subscribeToProduct: (productId: string, callback?: (data: any) => void) => () => void
  getConnectionHealth: () => {
    isHealthy: boolean
    lastPingTime: number
    averageLatency: number
    errorRate: number
  }
}

interface WebSocketProviderProps {
  children: ReactNode
  wsUrl?: string
  autoConnect?: boolean
  reconnectInterval?: number
  maxReconnectAttempts?: number
}

const WebSocketContext = createContext<WebSocketContextType | null>(null)

export const WebSocketProvider: React.FC<WebSocketProviderProps> = ({
  children,
  wsUrl,
  autoConnect = true,
  reconnectInterval = 3000,
  maxReconnectAttempts = 5
}) => {
  const { data: session } = useSession()
  const queryClient = useQueryClient()

  const wsRef = useRef<WebSocket | null>(null)
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | undefined>(undefined)
  const connectionTimeoutRef = useRef<NodeJS.Timeout | undefined>(undefined)
  const subscriptionsRef = useRef<Set<string>>(new Set())
  const subscribersRef = useRef<Map<string, Set<(data: any) => void>>>(new Map())
  const messageQueueRef = useRef<Array<{ message: any; timestamp: number }>>([])
  const messageBatchTimeoutRef = useRef<NodeJS.Timeout | undefined>(undefined)
  const lastMessageTimeRef = useRef<number>(0)
  const sessionRef = useRef(session)
  const reconnectAttemptsRef = useRef(0)
  const isInitializedRef = useRef(false)
  const isConnectingRef = useRef(false)
  const shouldReconnectRef = useRef(false) // Start with false, enable when needed
  const connectionDebounceRef = useRef<NodeJS.Timeout | undefined>(undefined)

  const [isConnected, setIsConnected] = useState(false)
  const [connectionStatus, setConnectionStatus] = useState<'connecting' | 'connected' | 'disconnected' | 'error'>('disconnected')
  const [lastMessage, setLastMessage] = useState<WebSocketMessage | null>(null)
  const [connectionStats, setConnectionStats] = useState({
    reconnectAttempts: 0,
    messagesReceived: 0,
    messagesSent: 0,
    uptime: 0,
    latency: 0,
    connectionQuality: 'unknown' as 'excellent' | 'good' | 'poor' | 'unknown',
    lastPingTime: 0,
    averageLatency: 0,
    errorRate: 0,
    lastError: '',
    healthScore: 100
  })

  // Enhanced connection monitoring
  const pingTimesRef = useRef<number[]>([])
  const lastPingTimeRef = useRef<number>(0)

  const connectStartTime = useRef<number>(0)

  // Update refs when session changes
  useEffect(() => {
    sessionRef.current = session
  }, [session])

  // Handle pong with latency calculation
  const handlePong = useCallback((timestamp: number) => {
    const latency = Date.now() - timestamp

    // Keep last 10 ping times for average calculation
    pingTimesRef.current.push(latency)
    if (pingTimesRef.current.length > 10) {
      pingTimesRef.current.shift()
    }

    const avgLatency = pingTimesRef.current.reduce((a, b) => a + b, 0) / pingTimesRef.current.length

    // Determine connection quality based on latency
    let quality: 'excellent' | 'good' | 'poor' | 'unknown' = 'unknown'
    if (avgLatency < 100) quality = 'excellent'
    else if (avgLatency < 300) quality = 'good'
    else quality = 'poor'

    setConnectionStats(prev => ({
      ...prev,
      latency: avgLatency,
      connectionQuality: quality
    }))
  }, [])

  // Enhanced ping with latency measurement
  const sendPing = useCallback(() => {
    if (wsRef.current?.readyState === WebSocket.OPEN) {
      const pingTime = Date.now()
      lastPingTimeRef.current = pingTime

      wsRef.current.send(JSON.stringify({
        type: 'ping',
        timestamp: pingTime
      }))
    }
  }, [])

  const handleWebSocketMessage = useCallback((message: WebSocketMessage) => {
    switch (message.type) {
      case 'broadcast':
        if (message.channel?.startsWith('product:')) {
          const productId = message.channel.split(':')[1]

          if (message.data.type === 'new_bid') {
            // Invalidate product queries for real-time bid updates
            queryClient.invalidateQueries({
              queryKey: ['products', 'detail', productId]
            })
            queryClient.invalidateQueries({
              queryKey: ['products', 'slug']
            })
            queryClient.invalidateQueries({
              queryKey: ['bidding', 'history', productId]
            })
          } else if (message.data.type === 'auction_extended') {
            // Invalidate product queries for auction extension updates
            queryClient.invalidateQueries({
              queryKey: ['products', 'detail', productId]
            })
            queryClient.invalidateQueries({
              queryKey: ['products', 'slug']
            })
            queryClient.invalidateQueries({
              queryKey: ['auction-extension', 'logs', productId]
            })
            queryClient.invalidateQueries({
              queryKey: ['auction-extension', 'stats', productId]
            })
          }
        }
        break

      case 'user_message':
        if (message.data.type === 'auto_bid_executed') {
          const productId = message.data.productId
          queryClient.invalidateQueries({
            queryKey: ['products', 'detail', productId]
          })
          queryClient.invalidateQueries({
            queryKey: ['bidding', 'history', productId]
          })
          queryClient.invalidateQueries({
            queryKey: ['auto-bid', productId]
          })
        }
        break

      case 'pong':
        // Handle pong response for latency measurement
        if (message.data?.timestamp) {
          handlePong(message.data.timestamp)
        }
        break
    }
  }, [queryClient, handlePong])

  const connectInternal = useCallback(() => {
    if (wsRef.current?.readyState === WebSocket.OPEN) {
      console.log('🔗 WebSocket already connected, skipping connection attempt')
      return
    }

    // Prevent multiple connection attempts
    if (wsRef.current?.readyState === WebSocket.CONNECTING || isConnectingRef.current) {
      console.log('🔗 WebSocket already connecting, skipping connection attempt')
      return
    }

    // Check if we should reconnect
    if (!shouldReconnectRef.current) {
      console.log('🔗 Reconnection disabled, skipping connection attempt')
      return
    }

    isConnectingRef.current = true

    // Clear any existing timeouts
    if (connectionTimeoutRef.current) {
      clearTimeout(connectionTimeoutRef.current)
      connectionTimeoutRef.current = undefined
    }
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current)
      reconnectTimeoutRef.current = undefined
    }

    const url = wsUrl || process.env.NEXT_PUBLIC_WS_URL || 'ws://localhost:3001'
    console.log(`🔗 Attempting to connect to WebSocket: ${url}`)

    try {
      setConnectionStatus('connecting')
      wsRef.current = new WebSocket(url)
      connectStartTime.current = Date.now()

      // Register connection with debugger
      const connectionId = `ws-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`
      wsDebugger.registerConnection(wsRef.current, connectionId, url)

      // Set connection timeout with retry logic
      connectionTimeoutRef.current = setTimeout(() => {
        if (wsRef.current?.readyState === WebSocket.CONNECTING) {
          console.warn('⏰ WebSocket connection timeout after 10 seconds')
          wsRef.current?.close()
          isConnectingRef.current = false

          // Retry connection if under max attempts
          if (reconnectAttemptsRef.current < maxReconnectAttempts) {
            const baseDelay = reconnectInterval * Math.pow(1.5, reconnectAttemptsRef.current)
            const jitter = Math.random() * 1000 // Add jitter
            const delay = Math.min(baseDelay + jitter, 30000) // Max 30 seconds

            console.log(`🔄 Connection timeout, retrying in ${Math.round(delay)}ms (attempt ${reconnectAttemptsRef.current + 1}/${maxReconnectAttempts})`)

            reconnectAttemptsRef.current += 1
            setConnectionStats(prev => ({
              ...prev,
              reconnectAttempts: reconnectAttemptsRef.current,
              lastError: 'Connection timeout'
            }))

            reconnectTimeoutRef.current = setTimeout(() => {
              if (shouldReconnectRef.current) {
                connectInternal()
              }
            }, delay)
          } else {
            setConnectionStatus('error')
            console.error('❌ Connection timeout - max attempts reached')
            setConnectionStats(prev => ({
              ...prev,
              lastError: 'Max connection attempts reached'
            }))
          }
        }
      }, 10000) // 10 second timeout

      wsRef.current.onopen = () => {
        // Clear connection timeout
        if (connectionTimeoutRef.current) {
          clearTimeout(connectionTimeoutRef.current)
          connectionTimeoutRef.current = undefined
        }
        console.log('✅ WebSocket connected successfully')
        isConnectingRef.current = false
        setIsConnected(true)
        setConnectionStatus('connected')

        // Reset reconnect attempts on successful connection
        reconnectAttemptsRef.current = 0
        const connectionTime = Date.now() - connectStartTime.current
        setConnectionStats(prev => ({
          ...prev,
          reconnectAttempts: 0,
          uptime: connectionTime,
          lastError: '',
          healthScore: 100
        }))

        console.log(`🎯 Connection established in ${connectionTime}ms`)

        // Authenticate if user is logged in
        const currentSession = sessionRef.current
        if (currentSession?.user?.id) {
          wsRef.current?.send(JSON.stringify({
            type: 'authenticate',
            userId: currentSession.user.id,
            token: currentSession.accessToken || 'temp-token',
            sessionId: currentSession.user.id
          }))
        }

        // Re-subscribe to all channels
        subscriptionsRef.current.forEach(channel => {
          wsRef.current?.send(JSON.stringify({
            type: 'subscribe',
            channel
          }))
        })

        // Send queued messages
        while (messageQueueRef.current.length > 0) {
          const queuedMessage = messageQueueRef.current.shift()
          wsRef.current?.send(JSON.stringify(queuedMessage))
        }
      }

      wsRef.current.onmessage = (event) => {
        try {
          const message: WebSocketMessage = JSON.parse(event.data)
          setLastMessage(message)
          setConnectionStats(prev => ({
            ...prev,
            messagesReceived: prev.messagesReceived + 1,
            uptime: Date.now() - connectStartTime.current
          }))

          // Handle different message types
          handleWebSocketMessage(message)

          // Handle channel-specific subscribers
          if (message.type === 'broadcast' && message.channel) {
            const channelSubscribers = subscribersRef.current.get(message.channel) || new Set()
            channelSubscribers.forEach(callback => {
              try {
                callback(message.data)
              } catch (error) {
                console.error('Error in subscriber callback:', error)
              }
            })
          }
        } catch (error) {
          console.error('Error parsing WebSocket message:', error)
        }
      }

      wsRef.current.onclose = (event) => {
        // Clear connection timeout
        if (connectionTimeoutRef.current) {
          clearTimeout(connectionTimeoutRef.current)
          connectionTimeoutRef.current = undefined
        }

        console.log(`WebSocket disconnected ${event.code}`)
        setIsConnected(false)
        setConnectionStatus('disconnected')

        // Handle different close codes
        if (event.code === 1000) {
          // Normal closure - don't reconnect
          console.log('WebSocket closed normally')
          return
        }

        // Enhanced error handling with different strategies for different close codes
        if (event.code === 1006 || event.code === 1005 || event.code === 1011 || event.code === 1012) {
          // Abnormal closure - attempt reconnection with exponential backoff
          if (reconnectAttemptsRef.current < maxReconnectAttempts && shouldReconnectRef.current) {
            const baseDelay = reconnectInterval
            const jitter = Math.random() * 1000 // Add jitter to prevent thundering herd
            const exponentialDelay = baseDelay * Math.pow(2, reconnectAttemptsRef.current)
            const delay = Math.min(exponentialDelay + jitter, 30000)

            console.log(`Connection lost (code: ${event.code}, reason: ${event.reason}), reconnecting in ${Math.round(delay)}ms (attempt ${reconnectAttemptsRef.current + 1}/${maxReconnectAttempts})`)

            reconnectAttemptsRef.current += 1
            setConnectionStats(prev => ({
              ...prev,
              reconnectAttempts: reconnectAttemptsRef.current,
              lastError: `Connection lost: ${event.code} - ${event.reason || 'Unknown'}`
            }))

            reconnectTimeoutRef.current = setTimeout(() => {
              if (shouldReconnectRef.current) {
                connectInternal()
              }
            }, delay)
          } else {
            const reason = reconnectAttemptsRef.current >= maxReconnectAttempts
              ? 'Max reconnection attempts reached'
              : 'Reconnection disabled'
            console.log(`${reason}, setting error state`)
            setConnectionStatus('error')
            setConnectionStats(prev => ({
              ...prev,
              lastError: reason
            }))
          }
        } else if (event.code === 1008 || event.code === 1003) {
          // Policy violation or unsupported data - don't reconnect
          console.log(`Connection terminated due to policy violation (${event.code}), not reconnecting`)
          setConnectionStatus('error')
          shouldReconnectRef.current = false
          setConnectionStats(prev => ({
            ...prev,
            lastError: `Policy violation: ${event.code} - ${event.reason || 'Unknown'}`
          }))
        }
      }

      wsRef.current.onerror = (error) => {
        // Clear connection timeout
        if (connectionTimeoutRef.current) {
          clearTimeout(connectionTimeoutRef.current)
          connectionTimeoutRef.current = undefined
        }

        console.error('❌ WebSocket error:', error)
        isConnectingRef.current = false

        // Update connection stats with error
        setConnectionStats(prev => ({
          ...prev,
          lastError: 'Connection error occurred',
          healthScore: Math.max(prev.healthScore - 20, 0)
        }))

        // Don't immediately set error state, let onclose handle reconnection
        if (wsRef.current?.readyState === WebSocket.CONNECTING) {
          console.log('🔄 Connection failed - will retry via onclose handler')
        }
      }
    } catch (error) {
      console.error('Failed to create WebSocket connection:', error)
      setConnectionStatus('error')
      isConnectingRef.current = false
    }
  }, [wsUrl, maxReconnectAttempts, reconnectInterval, handleWebSocketMessage])

  // Debounced connect function to prevent rapid reconnections
  const connect = useCallback(() => {
    console.log('🔗 Connect requested - enabling reconnection')
    shouldReconnectRef.current = true // Enable reconnection when manually connecting

    // Clear any existing debounce timeout
    if (connectionDebounceRef.current) {
      clearTimeout(connectionDebounceRef.current)
    }

    // Debounce connection attempts by 200ms
    connectionDebounceRef.current = setTimeout(() => {
      connectInternal()
    }, 200)
  }, [connectInternal])

  // Enhanced session management with better connection logic
  useEffect(() => {
    console.log(`🔍 Connection effect - autoConnect: ${autoConnect}, session: ${!!session?.user?.id}, initialized: ${isInitializedRef.current}, connected: ${isConnected}`)

    // Auto-connect when autoConnect is enabled, regardless of session for development
    if (autoConnect && !isInitializedRef.current && !isConnected) {
      console.log('🔗 Auto-connecting WebSocket...')
      shouldReconnectRef.current = true // Enable reconnection for auto-connect

      // Debounce connection to prevent rapid reconnections
      const connectTimeout = setTimeout(() => {
        connect()
        isInitializedRef.current = true
      }, 500) // Increased delay for stability

      return () => clearTimeout(connectTimeout)
    }
  }, [session, autoConnect, connect, isConnected])

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      console.log('🧹 WebSocket cleanup on unmount')
      shouldReconnectRef.current = false

      // Clear all timeouts
      if (connectionTimeoutRef.current) {
        clearTimeout(connectionTimeoutRef.current)
      }
      if (reconnectTimeoutRef.current) {
        clearTimeout(reconnectTimeoutRef.current)
      }
      if (connectionDebounceRef.current) {
        clearTimeout(connectionDebounceRef.current)
      }
      if (messageBatchTimeoutRef.current) {
        clearTimeout(messageBatchTimeoutRef.current)
      }

      // Close WebSocket connection
      if (wsRef.current) {
        wsRef.current.close(1000, 'Component unmounting')
      }

      // Clear all data structures
      subscriptionsRef.current.clear()
      subscribersRef.current.clear()
      messageQueueRef.current.length = 0
      pingTimesRef.current.length = 0
    }
  }, [])

  const disconnect = useCallback(() => {
    console.log('🔌 Disconnecting WebSocket')
    shouldReconnectRef.current = false
    isConnectingRef.current = false

    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current)
      reconnectTimeoutRef.current = undefined
    }

    if (connectionTimeoutRef.current) {
      clearTimeout(connectionTimeoutRef.current)
      connectionTimeoutRef.current = undefined
    }

    if (connectionDebounceRef.current) {
      clearTimeout(connectionDebounceRef.current)
      connectionDebounceRef.current = undefined
    }

    if (messageBatchTimeoutRef.current) {
      clearTimeout(messageBatchTimeoutRef.current)
      messageBatchTimeoutRef.current = undefined
    }

    if (wsRef.current) {
      wsRef.current.close(1000, 'Manual disconnect')
      wsRef.current = null
    }

    // Clear all data structures to prevent memory leaks
    setIsConnected(false)
    setConnectionStatus('disconnected')
    subscriptionsRef.current.clear()
    subscribersRef.current.clear()
    messageQueueRef.current.length = 0
    pingTimesRef.current.length = 0
    lastMessageTimeRef.current = 0
    lastPingTimeRef.current = 0
  }, [])

  const subscribe = useCallback((channel: string, callback?: (data: any) => void) => {
    // Prevent duplicate subscriptions
    if (subscriptionsRef.current.has(channel)) {
      console.log(`🔔 Already subscribed to channel: ${channel}`)

      // Still add callback if provided
      if (callback) {
        if (!subscribersRef.current.has(channel)) {
          subscribersRef.current.set(channel, new Set())
        }
        subscribersRef.current.get(channel)?.add(callback)
        console.log(`📝 Added callback for existing channel: ${channel}`)
      }
      return
    }

    console.log(`🔔 Subscribing to channel: ${channel}`)
    subscriptionsRef.current.add(channel)

    // Add callback to subscribers if provided
    if (callback) {
      if (!subscribersRef.current.has(channel)) {
        subscribersRef.current.set(channel, new Set())
      }
      subscribersRef.current.get(channel)?.add(callback)
      console.log(`📝 Added callback for channel: ${channel}`)
    }

    if (wsRef.current?.readyState === WebSocket.OPEN) {
      wsRef.current.send(JSON.stringify({
        type: 'subscribe',
        channel
      }))
      console.log(`✅ Sent subscribe message for: ${channel}`)
    } else {
      console.log(`⏳ WebSocket not ready, subscription queued for: ${channel}`)
    }
  }, [])

  const unsubscribe = useCallback((channel: string, callback?: (data: any) => void) => {
    console.log(`🔕 Unsubscribing from channel: ${channel}`)

    // Remove callback from subscribers if provided
    if (callback) {
      const channelSubscribers = subscribersRef.current.get(channel)
      if (channelSubscribers) {
        channelSubscribers.delete(callback)
        console.log(`🗑️ Removed callback for channel: ${channel}`)

        // Only unsubscribe if no more callbacks
        if (channelSubscribers.size === 0) {
          subscribersRef.current.delete(channel)
          subscriptionsRef.current.delete(channel)

          if (wsRef.current?.readyState === WebSocket.OPEN) {
            wsRef.current.send(JSON.stringify({
              type: 'unsubscribe',
              channel
            }))
            console.log(`❌ Sent unsubscribe message for: ${channel}`)
          }
        }
      }
    } else {
      // Remove all subscribers for this channel
      subscribersRef.current.delete(channel)
      subscriptionsRef.current.delete(channel)

      if (wsRef.current?.readyState === WebSocket.OPEN) {
        wsRef.current.send(JSON.stringify({
          type: 'unsubscribe',
          channel
        }))
        console.log(`❌ Sent unsubscribe message for: ${channel}`)
      }
    }
  }, [])

  // Flush message queue with batching
  const flushMessageQueue = useCallback(() => {
    if (messageBatchTimeoutRef.current) {
      clearTimeout(messageBatchTimeoutRef.current)
      messageBatchTimeoutRef.current = undefined
    }

    if (messageQueueRef.current.length === 0 || wsRef.current?.readyState !== WebSocket.OPEN) {
      return
    }

    const messagesToSend = messageQueueRef.current.splice(0, 10) // Send max 10 messages at once

    messagesToSend.forEach(({ message }) => {
      try {
        wsRef.current?.send(JSON.stringify(message))
        setConnectionStats(prev => ({
          ...prev,
          messagesSent: prev.messagesSent + 1
        }))
      } catch (error) {
        console.error('Error sending queued message:', error)
      }
    })

    // If there are more messages, schedule another flush
    if (messageQueueRef.current.length > 0) {
      messageBatchTimeoutRef.current = setTimeout(flushMessageQueue, 100)
    }
  }, [])

  const sendMessage = useCallback((message: any) => {
    const now = Date.now()

    if (wsRef.current?.readyState === WebSocket.OPEN) {
      // Throttle messages to prevent flooding (max 10 messages per second)
      const timeSinceLastMessage = now - lastMessageTimeRef.current
      if (timeSinceLastMessage < 100) { // 100ms throttle
        // Queue the message instead of sending immediately
        messageQueueRef.current.push({ message, timestamp: now })

        // Schedule batch processing if not already scheduled
        if (!messageBatchTimeoutRef.current) {
          messageBatchTimeoutRef.current = setTimeout(flushMessageQueue, 100)
        }
        return
      }

      try {
        wsRef.current.send(JSON.stringify(message))
        lastMessageTimeRef.current = now
        setConnectionStats(prev => ({
          ...prev,
          messagesSent: prev.messagesSent + 1
        }))
      } catch (error) {
        console.error('Error sending WebSocket message:', error)
        // Queue message on error
        messageQueueRef.current.push({ message, timestamp: now })
      }
    } else {
      console.warn('WebSocket is not connected, queueing message')
      // Queue message for when connection is restored
      messageQueueRef.current.push({ message, timestamp: now })

      // Limit queue size to prevent memory issues
      if (messageQueueRef.current.length > 100) {
        messageQueueRef.current.shift() // Remove oldest message
      }
    }
  }, [flushMessageQueue])

  // Initialize connection only once
  useEffect(() => {
    console.log(`🚀 WebSocket initialization effect - autoConnect: ${autoConnect}, isInitialized: ${isInitializedRef.current}`)

    if (autoConnect && !isInitializedRef.current) {
      console.log('🚀 Initializing WebSocket connection for the first time')
      isInitializedRef.current = true
      connect()
    }

    return () => {
      if (isInitializedRef.current) {
        console.log('🧹 Cleaning up WebSocket connection')
        disconnect()
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [autoConnect]) // Only depend on autoConnect to prevent infinite re-renders

  // Update uptime periodically and send pings
  useEffect(() => {
    if (isConnected) {
      const uptimeInterval = setInterval(() => {
        setConnectionStats(prev => ({
          ...prev,
          uptime: Date.now() - connectStartTime.current
        }))
      }, 1000)

      // Send ping every 30 seconds to measure latency
      const pingInterval = setInterval(() => {
        sendPing()
      }, 30000)

      return () => {
        clearInterval(uptimeInterval)
        clearInterval(pingInterval)
      }
    }
  }, [isConnected, sendPing])

  const reconnect = useCallback(() => {
    // Reset reconnection attempts and force reconnect
    console.log('🔄 Manual reconnect requested')
    shouldReconnectRef.current = true
    reconnectAttemptsRef.current = 0
    setConnectionStats(prev => ({ ...prev, reconnectAttempts: 0 }))
    disconnect()
    setTimeout(() => connect(), 100)
  }, [connect, disconnect])

  // Connection health monitoring
  const getConnectionHealth = useCallback(() => {
    const now = Date.now()
    const timeSinceLastPing = now - connectionStats.lastPingTime
    const isHealthy = isConnected &&
      connectionStats.errorRate < 0.1 &&
      connectionStats.averageLatency < 1000 &&
      timeSinceLastPing < 60000 // Less than 1 minute since last ping

    return {
      isHealthy,
      lastPingTime: connectionStats.lastPingTime,
      averageLatency: connectionStats.averageLatency,
      errorRate: connectionStats.errorRate,
      healthScore: connectionStats.healthScore,
      connectionQuality: connectionStats.connectionQuality,
      uptime: connectionStats.uptime,
      reconnectAttempts: connectionStats.reconnectAttempts
    }
  }, [isConnected, connectionStats])

  // Enhanced product subscription with automatic cleanup
  const subscribeToProduct = useCallback((productId: string, callback?: (data: any) => void) => {
    const channel = `product:${productId}`
    console.log(`🎯 Enhanced product subscription for: ${productId}`)

    subscribe(channel, callback)

    // Return cleanup function
    return () => {
      console.log(`🧹 Cleaning up product subscription for: ${productId}`)
      unsubscribe(channel, callback)
    }
  }, [subscribe, unsubscribe])





  const value: WebSocketContextType = {
    isConnected,
    connectionStatus,
    subscribe,
    unsubscribe,
    sendMessage,
    lastMessage,
    connectionStats,
    reconnect,
    disconnect,
    subscribeToProduct,
    getConnectionHealth
  }

  return (
    <WebSocketContext.Provider value={value}>
      {children}
    </WebSocketContext.Provider>
  )
}

export const useWebSocketContext = () => {
  const context = useContext(WebSocketContext)
  return context // Return null if not within provider instead of throwing
}

export const useWebSocketContextRequired = () => {
  const context = useContext(WebSocketContext)
  if (!context) {
    throw new Error('useWebSocketContextRequired must be used within a WebSocketProvider')
  }
  return context
}