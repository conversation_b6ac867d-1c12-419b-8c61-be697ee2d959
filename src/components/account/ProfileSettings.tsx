'use client'
import React, { useEffect, useState } from 'react'
import {
  Card,
  Heading,
  Text,
  VStack,
  HStack,
  Button,
  Input,
  Grid,
  GridItem,
  Badge,
  Alert,
  Separator,
  Spinner,
  Textarea,
  InputGroup,
} from '@chakra-ui/react'
import { FormControl, FormLabel, FormErrorMessage } from '@/components/ui/form'
import { useForm, Controller } from 'react-hook-form'
import { FaUser, FaEnvelope, FaMapMarkerAlt, FaShieldAlt, FaEdit, FaSave, FaTimes, FaCheckCircle, FaExclamationTriangle } from 'react-icons/fa'
import { useSession } from '@/hooks/useSession'
import {
  useProfileQuery,
  useUpdateProfileMutation,
  useSellerStatusQuery,
  useRequestVerificationMutation
} from '@/services/useProfileQuery'
import VerificationModal from './VerificationModal'
import UserAddressManager from './UserAddressManager'

interface ProfileFormData {
  firstName: string
  lastName: string
  phoneNumber: string
  sellerAddressName?: string
  sellerCountry?: string
  sellerProvince?: string
  sellerCity?: string
  sellerPostalCode?: string
  sellerFullAddress?: string
}

const ProfileSettings: React.FC = () => {
  const { user, isLoading: sessionLoading } = useSession()
  const [isEditing, setIsEditing] = useState(false)
  const [verificationModal, setVerificationModal] = useState<{
    isOpen: boolean
    email: string
  }>({
    isOpen: false,
    email: ''
  })

  // API Hooks
  const { data: profileData, isLoading: profileLoading } = useProfileQuery()
  const { data: sellerStatus, isLoading: sellerStatusLoading } = useSellerStatusQuery()
  const updateProfileMutation = useUpdateProfileMutation()
  const requestVerificationMutation = useRequestVerificationMutation()

  const isLoading = sessionLoading || profileLoading || sellerStatusLoading

  const {
    control,
    handleSubmit,
    reset,
    formState: { errors, isDirty }
  } = useForm<ProfileFormData>({
    defaultValues: {
      firstName: profileData?.firstName || user?.firstName || '',
      lastName: profileData?.lastName || user?.lastName || '',
      phoneNumber: profileData?.phoneNumber || user?.phoneNumber || '',
      sellerAddressName: profileData?.sellerAddressName || '',
      sellerCountry: profileData?.sellerCountry || '',
      sellerProvince: profileData?.sellerProvince || '',
      sellerCity: profileData?.sellerCity || '',
      sellerPostalCode: profileData?.sellerPostalCode || '',
      sellerFullAddress: profileData?.sellerFullAddress || ''
    }
  })

  useEffect(() => {
    if (profileData) {
      reset({
        firstName: profileData.firstName,
        lastName: profileData.lastName,
        phoneNumber: profileData.phoneNumber,
        sellerAddressName: profileData.sellerAddressName || '',
        sellerCountry: profileData.sellerCountry || '',
        sellerProvince: profileData.sellerProvince || '',
        sellerCity: profileData.sellerCity || '',
        sellerPostalCode: profileData.sellerPostalCode || '',
        sellerFullAddress: profileData.sellerFullAddress || ''
      })
    }
  }, [profileData, reset])

  const onSubmit = async (data: ProfileFormData) => {
    try {
      await updateProfileMutation.mutateAsync(data)
      setIsEditing(false)
    } catch (error) {
      console.error('Failed to update profile:', error)
    }
  }

  const handleCancel = () => {
    reset()
    setIsEditing(false)
  }

  const handleVerifyEmail = async () => {
    console.log('Requesting email verification for:', profileData)
    if (profileData?.email) {
      setVerificationModal({
        isOpen: true,
        email: profileData.email
      })

      await requestVerificationMutation.mutateAsync({
        type: 'email',
        value: profileData.email
      })
    }
  }

  const handleVerificationSuccess = () => {
    // Refresh seller status after successful verification
    window.location.reload()
  }

  if (isLoading) {
    return (
      <VStack gap={6} align="stretch">
        <Card.Root>
          <Card.Body p={6}>
            <VStack gap={4} align="center">
              <Spinner size="lg" />
              <Text>Loading profile...</Text>
            </VStack>
          </Card.Body>
        </Card.Root>
      </VStack>
    )
  }

  if (!user) {
    return (
      <Alert.Root status="error">
        <FaExclamationTriangle />
        <Alert.Title>Profile not found</Alert.Title>
        <Alert.Description>
          Unable to load your profile information. Please try refreshing the page.
        </Alert.Description>
      </Alert.Root>
    )
  }

  const canSell = sellerStatus?.canSell || false
  const emailVerified = sellerStatus?.emailVerified || false

  return (
    <VStack gap={6} align="stretch">
      <Card.Root>
        <Card.Body>
          <HStack justify="space-between" align="center">
            <VStack align="start" gap={1}>
              <Heading size="lg">Profile Settings</Heading>
              <Text color="gray.600">Manage your account information and seller verification</Text>
            </VStack>
            {!isEditing && (
              <Button
                colorScheme="blue"
                variant="outline"
                onClick={() => setIsEditing(true)}
              >
                <FaEdit />
                Edit Profile
              </Button>
            )}
          </HStack>
        </Card.Body>
      </Card.Root>

      {/* Verification Status */}
      {
        !canSell && (
          <Card.Root>
            <Card.Header>
              <HStack gap={2}>
                <FaShieldAlt color={canSell ? 'green' : 'orange'} />
                <Heading size="md">Verification Status</Heading>
                <Badge colorScheme={canSell ? 'green' : 'orange'}>
                  {canSell ? 'Verified Seller' : 'Verification Required'}
                </Badge>
              </HStack>
            </Card.Header>
            <Card.Body>
              <VStack gap={4} align="stretch">
                {!canSell && (
                  <Alert.Root status="warning">
                    <FaExclamationTriangle />
                    <Alert.Title>Complete verification to start selling</Alert.Title>
                    <Alert.Description>
                      You need to verify both your email and phone number before you can list items for sale.
                    </Alert.Description>
                  </Alert.Root>
                )}

                <Grid templateColumns="repeat(auto-fit, minmax(250px, 1fr))" gap={4}>
                  {/* Email Verification */}
                  <GridItem>
                    <HStack justify="space-between" p={4} border="1px solid" borderColor="gray.200" borderRadius="lg">
                      <HStack gap={3}>
                        <FaEnvelope color={emailVerified ? 'green' : 'gray'} />
                        <VStack align="start" gap={1}>
                          <Text fontWeight="medium">Email Verification</Text>
                          <Text fontSize="sm" color="gray.600">{user.email}</Text>
                        </VStack>
                      </HStack>
                      {emailVerified ? (
                        <FaCheckCircle color="green" />
                      ) : (
                        <Button size="sm" colorScheme="blue" onClick={handleVerifyEmail}>
                          Verify
                        </Button>
                      )}
                    </HStack>
                  </GridItem>
                </Grid>
              </VStack>
            </Card.Body>
          </Card.Root>
        )
      }

      {/* Profile Information */}
      <Card.Root>
        <Card.Header>
          <HStack gap={2}>
            <FaUser />
            <Heading size="md">Profile Information</Heading>
          </HStack>
        </Card.Header>
        <Card.Body>
          <form onSubmit={handleSubmit(onSubmit)}>
            <VStack gap={6} align="stretch">
              <Grid templateColumns={{ base: '1fr', md: 'repeat(2, 1fr)' }} gap={4}>
                {/* First Name */}
                <GridItem>
                  <FormControl isInvalid={!!errors.firstName}>
                    <FormLabel>First Name</FormLabel>
                    <Controller
                      name="firstName"
                      control={control}
                      rules={{ required: 'First name is required' }}
                      render={({ field }) => (
                        <Input
                          {...field}
                          placeholder="Enter your first name"
                          disabled={!isEditing}
                        />
                      )}
                    />
                    <FormErrorMessage>{errors.firstName?.message}</FormErrorMessage>
                  </FormControl>
                </GridItem>

                {/* Last Name */}
                <GridItem>
                  <FormControl isInvalid={!!errors.lastName}>
                    <FormLabel>Last Name</FormLabel>
                    <Controller
                      name="lastName"
                      control={control}
                      rules={{ required: 'Last name is required' }}
                      render={({ field }) => (
                        <Input
                          {...field}
                          placeholder="Enter your last name"
                          disabled={!isEditing}
                        />
                      )}
                    />
                    <FormErrorMessage>{errors.lastName?.message}</FormErrorMessage>
                  </FormControl>
                </GridItem>
              </Grid>

              {/* Email (Read-only) */}
              <FormControl>
                <FormLabel>Email Address</FormLabel>
                <InputGroup endElement={
                  emailVerified && (
                    <HStack gap={2} color="green.500" fontWeight="bold" fontSize="sm">
                      <FaCheckCircle className="mr-2" color="green" />
                      <Text>Email Verified</Text>
                    </HStack>
                  )
                }>
                  <Input
                    value={profileData?.email}
                    disabled
                    bg="gray.50"
                  />
                </InputGroup>
              </FormControl>

              {/* Phone Number */}
              <FormControl isInvalid={!!errors.phoneNumber}>
                <FormLabel>Phone Number</FormLabel>
                <Controller
                  name="phoneNumber"
                  control={control}
                  rules={{
                    required: 'Phone number is required',
                    pattern: {
                      value: /^[+]?[\d\s\-\(\)]+$/,
                      message: 'Please enter a valid phone number'
                    }
                  }}
                  render={({ field }) => (
                    <Input
                      {...field}
                      placeholder="Enter your phone number"
                      disabled={!isEditing}
                    />
                  )}
                />
                <FormErrorMessage>{errors.phoneNumber?.message}</FormErrorMessage>
              </FormControl>

              <Separator />

              {/* Seller Address Section */}
              <VStack gap={4} align="stretch">
                <FormLabel>
                  <HStack gap={2}>
                    <FaMapMarkerAlt />
                    <Text fontWeight="bold">Seller Address</Text>
                  </HStack>
                </FormLabel>

                {profileData?.sellerAddressComplete ? (
                  <Alert.Root status="info">
                    <Alert.Indicator />
                    <Alert.Content>
                      <Alert.Title>Address Completed</Alert.Title>
                      <Alert.Description>
                        Your seller address has been set and cannot be modified.
                      </Alert.Description>
                    </Alert.Content>
                  </Alert.Root>
                ) : (
                  <Text fontSize="sm" color="gray.600">
                    Complete your seller address to start selling. This information can only be set once.
                  </Text>
                )}

                <Grid templateColumns={{ base: '1fr', md: 'repeat(2, 1fr)' }} gap={4}>
                  {/* Address Name */}
                  <FormControl isInvalid={!!errors.sellerAddressName}>
                    <FormLabel>Address Name</FormLabel>
                    <Controller
                      name="sellerAddressName"
                      control={control}
                      render={({ field }) => (
                        <Input
                          {...field}
                          placeholder="e.g., Home, Office"
                          disabled={!isEditing || profileData?.sellerAddressComplete}
                        />
                      )}
                    />
                    <FormErrorMessage>{errors.sellerAddressName?.message}</FormErrorMessage>
                  </FormControl>

                  {/* Country */}
                  <FormControl isInvalid={!!errors.sellerCountry}>
                    <FormLabel>Country</FormLabel>
                    <Controller
                      name="sellerCountry"
                      control={control}
                      render={({ field }) => (
                        <Input
                          {...field}
                          placeholder="Enter country"
                          disabled={!isEditing || profileData?.sellerAddressComplete}
                        />
                      )}
                    />
                    <FormErrorMessage>{errors.sellerCountry?.message}</FormErrorMessage>
                  </FormControl>

                  {/* Province */}
                  <FormControl isInvalid={!!errors.sellerProvince}>
                    <FormLabel>Province/State</FormLabel>
                    <Controller
                      name="sellerProvince"
                      control={control}
                      render={({ field }) => (
                        <Input
                          {...field}
                          placeholder="Enter province or state"
                          disabled={!isEditing || profileData?.sellerAddressComplete}
                        />
                      )}
                    />
                    <FormErrorMessage>{errors.sellerProvince?.message}</FormErrorMessage>
                  </FormControl>

                  {/* City */}
                  <FormControl isInvalid={!!errors.sellerCity}>
                    <FormLabel>City</FormLabel>
                    <Controller
                      name="sellerCity"
                      control={control}
                      render={({ field }) => (
                        <Input
                          {...field}
                          placeholder="Enter city"
                          disabled={!isEditing || profileData?.sellerAddressComplete}
                        />
                      )}
                    />
                    <FormErrorMessage>{errors.sellerCity?.message}</FormErrorMessage>
                  </FormControl>

                  {/* Postal Code */}
                  <FormControl isInvalid={!!errors.sellerPostalCode}>
                    <FormLabel>Postal Code</FormLabel>
                    <Controller
                      name="sellerPostalCode"
                      control={control}
                      render={({ field }) => (
                        <Input
                          {...field}
                          placeholder="Enter postal code"
                          disabled={!isEditing || profileData?.sellerAddressComplete}
                        />
                      )}
                    />
                    <FormErrorMessage>{errors.sellerPostalCode?.message}</FormErrorMessage>
                  </FormControl>
                </Grid>

                {/* Full Address */}
                <FormControl isInvalid={!!errors.sellerFullAddress}>
                  <FormLabel>Full Address</FormLabel>
                  <Controller
                    name="sellerFullAddress"
                    control={control}
                    render={({ field }) => (
                      <Textarea
                        {...field}
                        placeholder="Enter complete address"
                        disabled={!isEditing || profileData?.sellerAddressComplete}
                      />
                    )}
                  />
                  <FormErrorMessage>{errors.sellerFullAddress?.message}</FormErrorMessage>
                </FormControl>

                <Text fontSize="xs" color="gray.500">
                  This address will be used for shipping your sold items and cannot be changed once completed.
                </Text>
              </VStack>

              {/* Action Buttons */}
              {isEditing && (
                <HStack gap={3} justify="end">
                  <Button
                    variant="outline"
                    onClick={handleCancel}
                    disabled={updateProfileMutation.isPending}
                  >
                    <FaTimes />
                    Cancel
                  </Button>
                  <Button
                    type="submit"
                    colorScheme="blue"
                    loading={updateProfileMutation.isPending}
                    disabled={!isDirty}
                  >
                    <FaSave />
                    Save Changes
                  </Button>
                </HStack>
              )}
            </VStack>
          </form>
        </Card.Body>
      </Card.Root>

      {/* Shipping Addresses Management */}
      <UserAddressManager />

      {/* Verification Modal */}
      <VerificationModal
        isOpen={verificationModal.isOpen}
        onClose={() => setVerificationModal(prev => ({ ...prev, isOpen: false }))}
        email={verificationModal.email}
        onSuccess={handleVerificationSuccess}
      />
    </VStack>
  )
}

export default ProfileSettings
