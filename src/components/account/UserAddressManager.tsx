'use client'
import React, { useState } from 'react'
import { useF<PERSON>, Controller } from 'react-hook-form'
import {
    Box,
    Button,
    Card,
    Flex,
    Grid,
    GridItem,
    Heading,
    HStack,
    Input,
    Text,
    Textarea,
    VStack,
    Badge,
    IconButton,
    Spinner,
    Dialog,
    Checkbox,
} from '@chakra-ui/react'
import { FormControl, FormLabel, FormErrorMessage } from '@/components/ui/form'
import FormSelectField, { SelectOption } from '@/components/ui/form/FormSelectField'
import { FaPlus, FaEdit, FaTrash, FaMapMarkerAlt } from 'react-icons/fa'
import { useShippingAddressesQuery, useCreateShippingAddressMutation, ShippingAddress } from '@/services/useCheckoutQuery'
import countriesData from '@/data/countries.json'
import { toaster } from '@/components/ui/toaster'

interface UserAddressManagerProps {
    className?: string
}

const UserAddressManager: React.FC<UserAddressManagerProps> = ({ className }) => {
    const [isAddingAddress, setIsAddingAddress] = useState(false)
    const [editingAddress, setEditingAddress] = useState<ShippingAddress | null>(null)

    // Queries and mutations
    const { data: addresses = [], isLoading, error } = useShippingAddressesQuery()
    const createAddressMutation = useCreateShippingAddressMutation()

    // Form setup
    const {
        control,
        handleSubmit,
        formState: { errors },
        reset,
        watch
    } = useForm<ShippingAddress>({
        defaultValues: {
            name: '',
            country: '',
            province: '',
            city: '',
            postalCode: '',
            fullAddress: '',
            isDefault: false
        }
    })

    // Convert countries to select options
    const countryOptions: SelectOption[] = countriesData.map((country: { code: string; name: string }) => ({
        value: country.name,
        label: country.name
    }))

    // Handle form submission
    const handleFormSubmit = async (data: ShippingAddress) => {
        try {
            await createAddressMutation.mutateAsync(data)
            reset()
            setIsAddingAddress(false)
            setEditingAddress(null)
        } catch (error) {
            console.error('Failed to save address:', error)
        }
    }

    // Handle edit address
    const handleEditAddress = (address: ShippingAddress) => {
        setEditingAddress(address)
        reset(address)
        setIsAddingAddress(true)
    }

    // Handle cancel
    const handleCancel = () => {
        reset()
        setIsAddingAddress(false)
        setEditingAddress(null)
    }

    if (isLoading) {
        return (
            <Box className={className}>
                <Flex justify="center" align="center" minH="200px">
                    <Spinner size="lg" />
                </Flex>
            </Box>
        )
    }

    if (error) {
        return (
            <Box className={className}>
                <Card.Root p={6}>
                    <Text color="red.500" textAlign="center">
                        Failed to load addresses. Please try again.
                    </Text>
                </Card.Root>
            </Box>
        )
    }

    return (
        <Box className={className}>
            <VStack gap={6} align="stretch">
                {/* Header */}
                <Flex justify="space-between" align="center">
                    <Heading size="lg">Shipping Addresses</Heading>
                    <Button
                        colorScheme="blue"
                        onClick={() => setIsAddingAddress(true)}
                        disabled={isAddingAddress}
                    >
                        <FaPlus className="mr-2" />
                        Add New Address
                    </Button>
                </Flex>

                {/* Address List */}
                {addresses.length > 0 ? (
                    <Grid templateColumns={{ base: '1fr', md: 'repeat(2, 1fr)', lg: 'repeat(3, 1fr)' }} gap={4}>
                        {addresses.map((address) => (
                            <Card.Root key={address.id} p={4} position="relative">
                                <VStack align="start" gap={2}>
                                    <HStack justify="space-between" w="full">
                                        <HStack>
                                            <FaMapMarkerAlt color="blue.500" />
                                            <Text fontWeight="bold" fontSize="md">
                                                {address.name}
                                            </Text>
                                        </HStack>
                                        {address.isDefault && (
                                            <Badge colorScheme="green" size="sm">
                                                Default
                                            </Badge>
                                        )}
                                    </HStack>

                                    <Text fontSize="sm" color="gray.700" lineHeight="short">
                                        {address.fullAddress}
                                    </Text>

                                    <Text fontSize="sm" color="gray.600">
                                        {address.city}, {address.province} {address.postalCode}
                                    </Text>

                                    <Text fontSize="sm" color="gray.600">
                                        {address.country}
                                    </Text>

                                    <HStack justify="end" w="full" pt={2}>
                                        <IconButton
                                            aria-label="Edit address"
                                            // icon={<FaEdit />}
                                            size="sm"
                                            variant="ghost"
                                            onClick={() => handleEditAddress(address)}
                                        />
                                        <IconButton
                                            aria-label="Delete address"
                                            // icon={<FaTrash />}
                                            size="sm"
                                            variant="ghost"
                                            colorScheme="red"
                                            onClick={() => {
                                                // TODO: Implement delete functionality
                                                toaster.create({
                                                    title: "Feature Coming Soon",
                                                    description: "Address deletion will be available soon.",
                                                    type: "info",
                                                })
                                            }}
                                        />
                                    </HStack>
                                </VStack>
                            </Card.Root>
                        ))}
                    </Grid>
                ) : (
                    <Card.Root p={8}>
                        <VStack gap={4}>
                            <FaMapMarkerAlt size={48} color="gray.400" />
                            <Text color="gray.500" textAlign="center">
                                No shipping addresses found. Add your first address to get started.
                            </Text>
                            <Button
                                colorScheme="blue"
                                onClick={() => setIsAddingAddress(true)}
                            >
                                <FaPlus className="mr-2" />
                                Add Your First Address
                            </Button>
                        </VStack>
                    </Card.Root>
                )}

                {/* Add/Edit Address Dialog */}
                <Dialog.Root open={isAddingAddress} onOpenChange={(e) => !e.open && handleCancel()}>
                    <Dialog.Backdrop />
                    <Dialog.Positioner>
                        <Dialog.Content maxW="2xl" mx={4}>
                            <Dialog.Header>
                                <Dialog.Title>
                                    {editingAddress ? 'Edit Address' : 'Add New Address'}
                                </Dialog.Title>
                                <Dialog.CloseTrigger onClick={handleCancel} />
                            </Dialog.Header>

                            <Dialog.Body>
                                <form onSubmit={handleSubmit(handleFormSubmit)}>
                                    <VStack gap={4} align="stretch">
                                        {/* Address Name */}
                                        <FormControl isInvalid={!!errors.name}>
                                            <FormLabel>Address Name</FormLabel>
                                            <Controller
                                                name="name"
                                                control={control}
                                                rules={{ required: 'Address name is required' }}
                                                render={({ field }) => (
                                                    <Input
                                                        {...field}
                                                        placeholder="e.g., Home, Office, etc."
                                                    />
                                                )}
                                            />
                                            <FormErrorMessage>
                                                {errors.name?.message}
                                            </FormErrorMessage>
                                        </FormControl>

                                        {/* Country */}
                                        <FormControl isInvalid={!!errors.country}>
                                            <FormLabel>Country</FormLabel>
                                            <Controller
                                                name="country"
                                                control={control}
                                                rules={{ required: 'Country is required' }}
                                                render={({ field }) => (
                                                    <FormSelectField
                                                        {...field}
                                                        options={countryOptions}
                                                        placeholder="Select country"
                                                        value={countryOptions.find(option => option.value === field.value)}
                                                    />
                                                )}
                                            />
                                            <FormErrorMessage>
                                                {errors.country?.message}
                                            </FormErrorMessage>
                                        </FormControl>

                                        {/* Province and City */}
                                        <Grid templateColumns={{ base: '1fr', md: '1fr 1fr' }} gap={4}>
                                            <GridItem>
                                                <FormControl isInvalid={!!errors.province}>
                                                    <FormLabel>Province</FormLabel>
                                                    <Controller
                                                        name="province"
                                                        control={control}
                                                        rules={{ required: 'Province is required' }}
                                                        render={({ field }) => (
                                                            <Input
                                                                {...field}
                                                                placeholder="Enter province"
                                                            />
                                                        )}
                                                    />
                                                    <FormErrorMessage>
                                                        {errors.province?.message}
                                                    </FormErrorMessage>
                                                </FormControl>
                                            </GridItem>

                                            <GridItem>
                                                <FormControl isInvalid={!!errors.city}>
                                                    <FormLabel>City</FormLabel>
                                                    <Controller
                                                        name="city"
                                                        control={control}
                                                        rules={{ required: 'City is required' }}
                                                        render={({ field }) => (
                                                            <Input
                                                                {...field}
                                                                placeholder="Enter city"
                                                            />
                                                        )}
                                                    />
                                                    <FormErrorMessage>
                                                        {errors.city?.message}
                                                    </FormErrorMessage>
                                                </FormControl>
                                            </GridItem>
                                        </Grid>

                                        {/* Postal Code */}
                                        <FormControl isInvalid={!!errors.postalCode}>
                                            <FormLabel>Postal Code</FormLabel>
                                            <Controller
                                                name="postalCode"
                                                control={control}
                                                rules={{ required: 'Postal code is required' }}
                                                render={({ field }) => (
                                                    <Input
                                                        {...field}
                                                        placeholder="Enter postal code"
                                                    />
                                                )}
                                            />
                                            <FormErrorMessage>
                                                {errors.postalCode?.message}
                                            </FormErrorMessage>
                                        </FormControl>

                                        {/* Full Address */}
                                        <FormControl isInvalid={!!errors.fullAddress}>
                                            <FormLabel>Full Address</FormLabel>
                                            <Controller
                                                name="fullAddress"
                                                control={control}
                                                rules={{ required: 'Full address is required' }}
                                                render={({ field }) => (
                                                    <Textarea
                                                        {...field}
                                                        placeholder="Enter complete address"
                                                        rows={3}
                                                    />
                                                )}
                                            />
                                            <FormErrorMessage>
                                                {errors.fullAddress?.message}
                                            </FormErrorMessage>
                                        </FormControl>

                                        {/* Set as Default */}
                                        <FormControl>
                                            <Controller
                                                name="isDefault"
                                                control={control}
                                                render={({ field: { value, onChange } }) => (
                                                    <Checkbox.Root
                                                        checked={value}
                                                        onCheckedChange={(value) => onChange(Boolean(value.checked))}
                                                    >
                                                        <Checkbox.HiddenInput />
                                                        <Checkbox.Control />
                                                        <Checkbox.Label>Set as default address</Checkbox.Label>
                                                    </Checkbox.Root>
                                                )}
                                            />
                                        </FormControl>
                                    </VStack>
                                </form>
                            </Dialog.Body>

                            <Dialog.Footer>
                                <HStack>
                                    <Button variant="outline" onClick={handleCancel}>
                                        Cancel
                                    </Button>
                                    <Button
                                        colorScheme="blue"
                                        onClick={handleSubmit(handleFormSubmit)}
                                        loading={createAddressMutation.isPending}
                                    >
                                        {editingAddress ? 'Update Address' : 'Add Address'}
                                    </Button>
                                </HStack>
                            </Dialog.Footer>
                        </Dialog.Content>
                    </Dialog.Positioner>
                </Dialog.Root>
            </VStack>
        </Box>
    )
}

export default UserAddressManager
