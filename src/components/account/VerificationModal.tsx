'use client'
import React, { useState } from 'react'
import {
  Button,
  Text,
  VStack,
  HStack,
  Input,
  Box,
  Dialog,
} from '@chakra-ui/react'
import { Alert } from '@/components/ui/alert'
import { FormControl, FormLabel, FormErrorMessage } from '@/components/ui/form'
import { useForm } from 'react-hook-form'
import { FaEnvelope, FaCheckCircle } from 'react-icons/fa'
import { useConfirmVerificationMutation } from '@/services/useProfileQuery'

interface VerificationModalProps {
  isOpen: boolean
  onClose: () => void
  email: string
  onSuccess?: () => void
}

interface VerificationFormData {
  code: string
}

const VerificationModal: React.FC<VerificationModalProps> = ({
  isOpen,
  onClose,
  email,
  onSuccess
}) => {
  const [step, setStep] = useState<'info' | 'code'>('info')
  const confirmVerificationMutation = useConfirmVerificationMutation()

  const {
    register,
    handleSubmit,
    reset,
    formState: { errors }
  } = useForm<VerificationFormData>({
    defaultValues: {
      code: ''
    }
  })

  const handleClose = () => {
    setStep('info')
    reset()
    onClose()
  }

  const handleProceedToCode = () => {
    setStep('code')
  }

  const onSubmit = async (data: VerificationFormData) => {
    try {
      await confirmVerificationMutation.mutateAsync({
        type: 'email',
        code: data.code
      })

      if (onSuccess) {
        onSuccess()
      }

      handleClose()
    } catch (error) {
      console.error('Verification failed:', error)
    }
  }

  return (
    <Dialog.Root open={isOpen} onOpenChange={(e) => !e.open && handleClose()}>
      <Dialog.Backdrop />
      <Dialog.Positioner>
        <Dialog.Content maxW="md" mx={4} p={6}>
          <Dialog.Header>
            <Dialog.Title>
              <HStack gap={3}>
                <FaEnvelope size={20} />
                <Text>Email Verification</Text>
              </HStack>
            </Dialog.Title>
            {/* <DialogCloseTrigger /> */}
          </Dialog.Header>

          <Dialog.Body>
            {step === 'info' ? (
              <VStack gap={4} align="stretch">
                <Alert status="info">
                  <Alert.Icon />
                  <Box>
                    <Alert.Title>Verification Required</Alert.Title>
                    <Alert.Description>
                      To start selling on our platform, you need to verify your email address.
                    </Alert.Description>
                  </Box>
                </Alert>

                <VStack gap={2} align="start">
                  <Text fontWeight="medium">Email Address:</Text>
                  <Text color="gray.600" fontSize="sm">
                    {email}
                  </Text>
                </VStack>

                <Text fontSize="sm" color="gray.600">
                  Click "Send Code" to receive a verification code via email.
                </Text>
              </VStack>
            ) : (
              <form onSubmit={handleSubmit(onSubmit)}>
                <VStack gap={4} align="stretch">
                  <Text fontSize="sm" color="gray.600">
                    We've sent a verification code to {email}. Please enter the 6-digit code below.
                  </Text>

                  <FormControl isInvalid={!!errors.code}>
                    <FormLabel>Verification Code</FormLabel>
                    <Input
                      {...register('code', {
                        required: 'Verification code is required',
                        minLength: {
                          value: 6,
                          message: 'Code must be 6 digits'
                        },
                        maxLength: {
                          value: 6,
                          message: 'Code must be 6 digits'
                        }
                      })}
                      placeholder="Enter 6-digit code"
                      maxLength={6}
                      textAlign="center"
                      fontSize="lg"
                      letterSpacing="0.5em"
                    />
                    <FormErrorMessage>{errors.code?.message}</FormErrorMessage>
                  </FormControl>

                  <Text fontSize="xs" color="gray.500" textAlign="center">
                    Didn't receive the code? Check your spam folder or try again.
                  </Text>
                </VStack>
              </form>
            )}
          </Dialog.Body>

          <Dialog.Footer>
            <HStack gap={3}>
              {/* <DialogActionTrigger asChild>
              <Button variant="outline">Cancel</Button>
            </DialogActionTrigger> */}

              {step === 'info' ? (
                <Button
                  colorScheme="blue"
                  onClick={handleProceedToCode}
                >
                  Send Code
                </Button>
              ) : (
                <Button
                  type="submit"
                  colorScheme="blue"
                  onClick={handleSubmit(onSubmit)}
                  loading={confirmVerificationMutation.isPending}
                  disabled={confirmVerificationMutation.isPending}
                >
                  <FaCheckCircle />
                  Verify
                </Button>
              )}
            </HStack>
          </Dialog.Footer>
        </Dialog.Content>
      </Dialog.Positioner>
    </Dialog.Root>
  )
}

export default VerificationModal
