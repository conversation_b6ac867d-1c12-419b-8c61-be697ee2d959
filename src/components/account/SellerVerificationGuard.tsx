'use client'
import React from 'react'
import {
  Box,
  Card,
  Heading,
  Text,
  VStack,
  HStack,
  <PERSON>ton,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  Spinner,
  Grid,
  GridItem,
} from '@chakra-ui/react'
import { FaShieldAlt, FaExclamationTriangle, FaCheckCircle, FaEnvelope, FaPhone, FaCog } from 'react-icons/fa'
import { useRouter } from 'next/navigation'
import { useSellerStatusQuery } from '@/services/useProfileQuery'
import { useSession } from '@/hooks/useSession'

interface SellerVerificationGuardProps {
  children: React.ReactNode
  redirectTo?: string
}

const SellerVerificationGuard: React.FC<SellerVerificationGuardProps> = ({
  children,
  redirectTo = '/account/setting'
}) => {
  const router = useRouter()
  const { user, isLoading: sessionLoading } = useSession()
  const { data: sellerStatus, isLoading: sellerStatusLoading } = useSellerStatusQuery()

  const isLoading = sessionLoading || sellerStatusLoading

  if (isLoading) {
    return (
      <VStack gap={6} align="stretch" py={8}>
        <Card.Root>
          <Card.Body p={6}>
            <VStack gap={4} align="center">
              <Spinner size="lg" />
              <Text>Checking seller verification status...</Text>
            </VStack>
          </Card.Body>
        </Card.Root>
      </VStack>
    )
  }

  if (!user) {
    return (
      <VStack gap={6} align="stretch" py={8}>
        <Card.Root>
          <Card.Body p={6}>
            <Alert.Root status="error">
              <FaExclamationTriangle />
              <Alert.Title>Authentication Required</Alert.Title>
              <Alert.Description>
                You need to be logged in to access this page.
              </Alert.Description>
            </Alert.Root>
          </Card.Body>
        </Card.Root>
      </VStack>
    )
  }

  // If user can sell, render children
  if (sellerStatus?.canSell) {
    return <>{children}</>
  }

  // Show verification requirements
  return (
    <VStack gap={6} align="stretch" py={8} maxW="4xl" mx="auto" px={4}>
      {/* Header */}
      <Card.Root>
        <Card.Header>
          <HStack gap={3} align="center">
            <FaShieldAlt color="orange" size={24} />
            <VStack align="start" gap={1}>
              <Heading size="lg">Seller Verification Required</Heading>
              <Text color="gray.600">Complete your verification to start selling</Text>
            </VStack>
            <Badge colorScheme="orange" ml="auto">
              Verification Pending
            </Badge>
          </HStack>
        </Card.Header>
      </Card.Root>

      {/* Alert */}
      <Alert.Root status="warning">
        <FaExclamationTriangle />
        <Alert.Title>Complete verification to start selling</Alert.Title>
        <Alert.Description>
          To ensure a safe marketplace for all users, you need to verify your email and phone number before listing items for sale.
        </Alert.Description>
      </Alert.Root>

      {/* Verification Status */}
      <Card.Root>
        <Card.Header>
          <Heading size="md">Verification Checklist</Heading>
        </Card.Header>
        <Card.Body>
          <Grid templateColumns="repeat(auto-fit, minmax(300px, 1fr))" gap={4}>
            {/* Email Verification */}
            <GridItem>
              <HStack justify="space-between" p={4} border="1px solid" borderColor="gray.200" borderRadius="lg">
                <HStack gap={3}>
                  <FaEnvelope color={sellerStatus?.emailVerified ? 'green' : 'gray'} />
                  <VStack align="start" gap={1}>
                    <Text fontWeight="medium">Email Verification</Text>
                    <Text fontSize="sm" color="gray.600">{user.email}</Text>
                  </VStack>
                </HStack>
                {sellerStatus?.emailVerified ? (
                  <FaCheckCircle color="green" />
                ) : (
                  <Badge colorScheme="red">Required</Badge>
                )}
              </HStack>
            </GridItem>

            {/* Phone Verification */}
            <GridItem>
              <HStack justify="space-between" p={4} border="1px solid" borderColor="gray.200" borderRadius="lg">
                <HStack gap={3}>
                  <FaPhone color={sellerStatus?.phoneVerified ? 'green' : 'gray'} />
                  <VStack align="start" gap={1}>
                    <Text fontWeight="medium">Phone Verification</Text>
                    <Text fontSize="sm" color="gray.600">
                      {user.phoneNumber || 'No phone number'}
                    </Text>
                  </VStack>
                </HStack>
                {sellerStatus?.phoneVerified ? (
                  <FaCheckCircle color="green" />
                ) : (
                  <Badge colorScheme="red">Required</Badge>
                )}
              </HStack>
            </GridItem>
          </Grid>

          {/* Missing Requirements */}
          {sellerStatus?.missingRequirements && sellerStatus.missingRequirements.length > 0 && (
            <Box mt={6} p={4} bg="orange.50" border="1px solid" borderColor="orange.200" borderRadius="lg">
              <Text fontWeight="medium" color="orange.800" mb={2}>
                Missing Requirements:
              </Text>
              <VStack align="start" gap={1}>
                {sellerStatus.missingRequirements.map((requirement, index) => (
                  <Text key={index} fontSize="sm" color="orange.700">
                    • {requirement}
                  </Text>
                ))}
              </VStack>
            </Box>
          )}
        </Card.Body>
      </Card.Root>

      {/* Action Buttons */}
      <Card.Root>
        <Card.Body>
          <VStack gap={4} align="stretch">
            <Text textAlign="center" color="gray.600">
              Complete your verification in your profile settings to start selling.
            </Text>
            
            <HStack gap={3} justify="center">
              <Button
                variant="outline"
                onClick={() => router.back()}
              >
                Go Back
              </Button>
              
              <Button
                colorScheme="blue"
                onClick={() => router.push(redirectTo)}
              >
                <FaCog className="mr-2" />
                Complete Verification
              </Button>
            </HStack>
          </VStack>
        </Card.Body>
      </Card.Root>

      {/* Help Text */}
      <Box textAlign="center" py={4}>
        <Text fontSize="sm" color="gray.500">
          Having trouble with verification? Contact our support team for assistance.
        </Text>
      </Box>
    </VStack>
  )
}

export default SellerVerificationGuard
