'use client'
import React from 'react'
import { Controller, useFormContext } from 'react-hook-form'
import {
    Box,
    Card,
    Heading,
    Text,
    VStack,
    Textarea,
} from '@chakra-ui/react'
import { FormControl, FormLabel } from '@/components/ui/form'
import { CheckoutContext, CheckoutFormData } from '@/services/useCheckoutQuery'
import PaymentMethodSelector from '@/components/payment/PaymentMethodSelector'
import ShippingOptionsSelector from '@/components/shipping/ShippingOptionsSelector'
import AddressSelector from '@/components/checkout/AddressSelector'
import { useCurrencyLanguage } from '@/contexts/CurrencyLanguageContext'
import { ShippingAddress } from '@/services/useCheckoutQuery'

interface CheckoutFormProps {
    checkoutContext: CheckoutContext
}

const CheckoutForm: React.FC<CheckoutFormProps> = ({
    checkoutContext,
}) => {
    const { currency } = useCurrencyLanguage();

    const {
        control,
        formState: { errors },
        watch,
        setValue
    } = useFormContext<CheckoutFormData>();

    return (
        <Card.Root>
            <Card.Header>
                <Heading size="lg">Checkout Information</Heading>
                <Text color="gray.600" fontSize="sm">
                    Please fill in your details to complete the order
                </Text>
            </Card.Header>

            <Card.Body>
                <VStack gap={6} align="stretch">
                    {/* Shipping Address Section */}
                    <AddressSelector
                        onAddressSelect={(address: ShippingAddress) => {
                            // Update form values with selected address
                            setValue('shippingAddressId', address.id)
                            setValue('name', address.name)
                            setValue('address', address.fullAddress)
                            setValue('city', address.city)
                            setValue('provinceRegion', address.province)
                            setValue('zipCode', address.postalCode)
                            setValue('country', address.country)
                        }}
                        selectedAddressId={watch('shippingAddressId')}
                    />

                    {/* Shipping Options */}
                    <Box>
                        <Heading size="md" mb={4}>Shipping Options</Heading>
                        <Controller
                            name="shippingOption"
                            control={control}
                            rules={{ required: 'Please select a shipping option' }}
                            render={({ field }) => (
                                <ShippingOptionsSelector
                                    shippingAddress={{
                                        country: watch('country') || '',
                                        city: watch('city') || '',
                                        zipCode: watch('zipCode') || ''
                                    }}
                                    cartItems={checkoutContext.type === 'cart' ? checkoutContext.items.map(item => ({
                                        id: item.id,
                                        quantity: item.quantity,
                                        product: {
                                            id: item.product.id,
                                            itemName: item.product.itemName,
                                            weight: 1 // Default weight
                                        }
                                    })) : [{
                                        id: '1',
                                        quantity: checkoutContext.type === 'buy-now' ? checkoutContext.quantity : 1,
                                        product: {
                                            id: checkoutContext.product.id,
                                            itemName: checkoutContext.product.itemName,
                                            weight: 1 // Default weight
                                        }
                                    }]}
                                    onShippingSelect={(option) => {
                                        field.onChange({
                                            id: `${option.carrier}-${option.service}`,
                                            name: `${option.carrier} - ${option.service}`,
                                            cost: option.cost,
                                            estimatedDays: option.estimatedDays
                                        })
                                    }}
                                    selectedOption={field.value ? {
                                        carrier: field.value.id.split('-')[0],
                                        service: field.value.id.split('-')[1],
                                        cost: field.value.cost,
                                        currency: currency,
                                        estimatedDays: field.value.estimatedDays,
                                        trackingAvailable: true
                                    } : undefined}
                                />
                            )}
                        />
                        {errors.shippingOption && (
                            <Text color="red.500" fontSize="sm" mt={2}>
                                {errors.shippingOption.message}
                            </Text>
                        )}
                    </Box>

                    {/* Payment Method */}
                    <Box>
                        <Heading size="md" mb={4}>Payment Method</Heading>
                        <Controller
                            name="paymentMethod"
                            control={control}
                            rules={{ required: 'Please select a payment method' }}
                            render={({ field }) => (
                                <PaymentMethodSelector
                                    currency={currency}
                                    onMethodSelect={(method, type, channel) => {
                                        field.onChange(type)
                                        setValue('selectedPaymentMethod', method)
                                        setValue('ewalletType', type === 'ewallet' ? channel : undefined)
                                        setValue('paymentChannel', channel)
                                        setValue('bankCode', type === 'virtual_account' ? channel : undefined)
                                        setValue('retailOutletName', type === 'retail_outlet' ? channel : undefined)
                                    }}
                                    selectedMethod={field.value}
                                    selectedType={watch('selectedPaymentMethod')}
                                    selectedChannel={watch('paymentChannel')}
                                />
                            )}
                        />
                        {errors.paymentMethod && (
                            <Text color="red.500" fontSize="sm" mt={2}>
                                {errors.paymentMethod.message}
                            </Text>
                        )}
                    </Box>

                    {/* Order Notes */}
                    <Box>
                        <FormControl>
                            <FormLabel>Order Notes (Optional)</FormLabel>
                            <Controller
                                name="notes"
                                control={control}
                                render={({ field }) => (
                                    <Textarea
                                        {...field}
                                        placeholder="Any special instructions for your order..."
                                        rows={3}
                                    />
                                )}
                            />
                        </FormControl>
                    </Box>
                </VStack>
            </Card.Body>
        </Card.Root>
    )
}

export default CheckoutForm
