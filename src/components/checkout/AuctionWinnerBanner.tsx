'use client'
import React from 'react'
import {
    Box,
    Heading,
    Text,
    VStack,
    HStack,
    Grid,
    Badge,

} from '@chakra-ui/react'
import { FaTrophy, FaClock, FaCheckCircle } from 'react-icons/fa'
import { formatJakarta } from '@/utils/timezone'
import { useCurrencyLanguage } from '@/contexts/CurrencyLanguageContext'

interface AuctionWinnerBannerProps {
    bidData: {
        id: string
        amount: number
        bidderId: string
        bidder?: {
            id: string
            firstName: string
            lastName: string
        }
    }
    product: {
        id: string
        itemName: string
        auctionEndDate: string
    }
    paymentDeadline: string
    hoursRemaining: number
}

const AuctionWinnerBanner: React.FC<AuctionWinnerBannerProps> = ({
    bidData,
    product,
    paymentDeadline,
    hoursRemaining
}) => {
    const { formatPrice, convertPrice } = useCurrencyLanguage();
    // Calculate deadline urgency
    const isUrgent = hoursRemaining <= 24
    const isVeryUrgent = hoursRemaining <= 6

    // Determine status color scheme
    const getStatusColor = () => {
        if (isVeryUrgent) return 'red'
        if (isUrgent) return 'orange'
        return 'green'
    }

    const statusColor = getStatusColor()

    return (
        <Box
            p={6}
            bg={`${statusColor}.50`}
            border="2px solid"
            borderColor={`${statusColor}.200`}
            borderRadius="lg"
            position="relative"
            overflow="hidden"
        >
            {/* Background Pattern */}
            <Box
                position="absolute"
                top={0}
                left={0}
                right={0}
                bottom={0}
                opacity={0.05}
                bgImage="radial-gradient(circle, currentColor 1px, transparent 1px)"
                bgSize="20px 20px"
                pointerEvents="none"
            />

            <VStack gap={4} align="stretch" position="relative">
                {/* Header */}
                <HStack gap={3} justify="center">
                    <Box color={`${statusColor}.500`} fontSize="2xl">
                        <FaTrophy />
                    </Box>
                    <Heading size="lg" color={`${statusColor}.700`} textAlign="center">
                        Congratulations! You Won This Auction!
                    </Heading>
                </HStack>

                {/* Auction Details */}
                <Grid templateColumns={{ base: '1fr', md: '1fr 1fr 1fr' }} gap={4}>
                    <VStack gap={1}>
                        <Text fontSize="sm" color="gray.600">Your Winning Bid</Text>
                        <Text fontSize="xl" fontWeight="bold" color={`${statusColor}.600`}>
                            {formatPrice(convertPrice(bidData.amount, 'USD'))}
                        </Text>
                    </VStack>
                    <VStack gap={1}>
                        <Text fontSize="sm" color="gray.600">Auction Ended</Text>
                        <Text fontSize="md" fontWeight="semibold" color="gray.700">
                            {formatJakarta(product.auctionEndDate, 'DD MMMM YYYY HH:mm')}
                        </Text>
                    </VStack>
                    <VStack gap={1}>
                        <Text fontSize="sm" color="gray.600">Product</Text>
                        <Text fontSize="md" fontWeight="semibold" color="gray.700" textAlign="center">
                            {product.itemName}
                        </Text>
                    </VStack>
                </Grid>

                {/* Payment Deadline Section */}
                <Box
                    p={4}
                    bg="white"
                    borderRadius="md"
                    border="1px solid"
                    borderColor={`${statusColor}.200`}
                >
                    <VStack gap={3} align="stretch">
                        <HStack justify="space-between" align="center">
                            <HStack gap={2}>
                                <Box color={`${statusColor}.500`}>
                                    <FaClock />
                                </Box>
                                <Text fontWeight="semibold" color="gray.700">
                                    Payment Deadline
                                </Text>
                            </HStack>
                            <Badge
                                colorScheme={statusColor}
                                variant={isUrgent ? 'solid' : 'subtle'}
                                fontSize="sm"
                            >
                                {hoursRemaining}h remaining
                            </Badge>
                        </HStack>

                        <Text fontSize="sm" color="gray.600">
                            Complete your payment by{' '}
                            <Text as="span" fontWeight="semibold" color="gray.800">
                                {formatJakarta(paymentDeadline, 'DD MMMM YYYY HH:mm')} WIB
                            </Text>
                        </Text>

                        {/* Progress Bar */}
                        <Box>
                            {/* <Progress 
                                value={progressPercentage} 
                                colorScheme={statusColor}
                                size="sm"
                                borderRadius="full"
                            /> */}
                            <HStack justify="space-between" mt={1}>
                                <Text fontSize="xs" color="gray.500">
                                    Auction ended
                                </Text>
                                <Text fontSize="xs" color="gray.500">
                                    Payment deadline
                                </Text>
                            </HStack>
                        </Box>

                        {/* Urgency Messages */}
                        {isVeryUrgent && (
                            <Box
                                p={3}
                                bg="red.100"
                                borderRadius="md"
                                border="1px solid"
                                borderColor="red.200"
                            >
                                <HStack gap={2}>
                                    <Box color="red.500">
                                        <FaClock />
                                    </Box>
                                    <Text fontSize="sm" fontWeight="semibold" color="red.700">
                                        ⚠️ Urgent: Less than 6 hours remaining!
                                    </Text>
                                </HStack>
                                <Text fontSize="xs" color="red.600" mt={1}>
                                    Please complete your payment immediately to secure your item.
                                </Text>
                            </Box>
                        )}

                        {isUrgent && !isVeryUrgent && (
                            <Box
                                p={3}
                                bg="orange.100"
                                borderRadius="md"
                                border="1px solid"
                                borderColor="orange.200"
                            >
                                <HStack gap={2}>
                                    <Box color="orange.500">
                                        <FaClock />
                                    </Box>
                                    <Text fontSize="sm" fontWeight="semibold" color="orange.700">
                                        ⏰ Less than 24 hours remaining
                                    </Text>
                                </HStack>
                                <Text fontSize="xs" color="orange.600" mt={1}>
                                    Don't miss out! Complete your payment soon.
                                </Text>
                            </Box>
                        )}

                        {!isUrgent && (
                            <Box
                                p={3}
                                bg="green.100"
                                borderRadius="md"
                                border="1px solid"
                                borderColor="green.200"
                            >
                                <HStack gap={2}>
                                    <Box color="green.500">
                                        <FaCheckCircle />
                                    </Box>
                                    <Text fontSize="sm" fontWeight="semibold" color="green.700">
                                        You have plenty of time to complete payment
                                    </Text>
                                </HStack>
                                <Text fontSize="xs" color="green.600" mt={1}>
                                    Take your time to review your order details.
                                </Text>
                            </Box>
                        )}
                    </VStack>
                </Box>
            </VStack>
        </Box>
    )
}

export default AuctionWinnerBanner
