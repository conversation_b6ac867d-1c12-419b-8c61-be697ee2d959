'use client'
import React from 'react'
import {
    Box,
    Card,
    Heading,
    Text,
    VStack,
    HStack,
    Image,
    Badge,
    Separator,
    Button,

} from '@chakra-ui/react'
import { CheckoutContext } from '@/services/useCheckoutQuery'
import { useCurrencyLanguage } from '@/contexts/CurrencyLanguageContext'
import { FaLock } from 'react-icons/fa'

interface CheckoutSummaryProps {
    checkoutContext: CheckoutContext
    onCompletePayment?: () => void
    isSubmitting?: boolean
}

const CheckoutSummary: React.FC<CheckoutSummaryProps> = ({
    checkoutContext,
    onCompletePayment,
    isSubmitting = false
}) => {
    const { currency, formatPrice, convertPrice } = useCurrencyLanguage()

    // Calculate totals based on checkout type
    const calculateTotals = () => {
        let subtotal = 0
        let itemCount = 0

        switch (checkoutContext.type) {
            case 'cart':
                subtotal = checkoutContext.items.reduce((sum, item) => sum + (item.price * item.quantity), 0)
                itemCount = checkoutContext.totalItems
                break
            case 'buy-now':
                subtotal = checkoutContext.product.priceUSD
                itemCount = checkoutContext.quantity
                break
            case 'bidding':
                subtotal = checkoutContext.winningBid.amount
                itemCount = 1
                break
        }

        // Convert to display currency if needed
        const displaySubtotal = currency === 'IDR' ? subtotal * 15000 : subtotal // Simplified conversion
        const shippingCost = currency === 'IDR' ? 310000 : 20.00
        const tax = displaySubtotal * 0.1
        const total = displaySubtotal + shippingCost + tax

        return {
            subtotal: displaySubtotal,
            shippingCost,
            tax,
            total,
            itemCount
        }
    }

    const totals = calculateTotals()

    // Render product items
    const renderItems = () => {
        switch (checkoutContext.type) {
            case 'cart':
                return checkoutContext.items.map((item) => (
                    <Box key={item.id} p={4} border="1px solid" borderColor="gray.200" borderRadius="md">
                        <HStack gap={4} align="start">
                            <Image
                                src={item.product.images.find(img => img.isMain)?.imageUrl || '/placeholder.jpg'}
                                alt={item.product.itemName}
                                boxSize="60px"
                                objectFit="cover"
                                borderRadius="md"
                            />
                            <VStack align="start" flex="1" gap={1}>
                                <Text fontWeight="semibold" fontSize="sm" lineHeight="short">
                                    {item.product.itemName}
                                </Text>
                                <HStack gap={2}>
                                    <Text fontSize="sm" color="gray.600">
                                        Qty: {item.quantity}
                                    </Text>
                                    <Text fontSize="sm" fontWeight="semibold">
                                        {formatPrice(convertPrice(item.price, 'USD'))}
                                    </Text>
                                </HStack>
                            </VStack>
                        </HStack>
                    </Box>
                ))

            case 'buy-now':
                return (
                    <Box p={4} border="1px solid" borderColor="gray.200" borderRadius="md">
                        <HStack gap={4} align="start">
                            <Image
                                src={checkoutContext.product.images.find(img => img.isMain)?.imageUrl || '/placeholder.jpg'}
                                alt={checkoutContext.product.itemName}
                                boxSize="60px"
                                objectFit="cover"
                                borderRadius="md"
                            />
                            <VStack align="start" flex="1" gap={1}>
                                <Text fontWeight="semibold" fontSize="sm" lineHeight="short">
                                    {checkoutContext.product.itemName}
                                </Text>
                                <HStack gap={2}>
                                    <Badge colorScheme="blue" variant="subtle">
                                        Buy Now
                                    </Badge>
                                    <Text fontSize="sm" fontWeight="semibold">
                                        {formatPrice(convertPrice(checkoutContext.product.priceUSD, 'USD'))}
                                    </Text>
                                </HStack>
                            </VStack>
                        </HStack>
                    </Box>
                )

            case 'bidding':
                return (
                    <Box p={4} border="1px solid" borderColor="green.200" borderRadius="md" bg="green.50">
                        <HStack gap={4} align="start">
                            <Image
                                src={checkoutContext.product.images.find(img => img.isMain)?.imageUrl || '/placeholder.jpg'}
                                alt={checkoutContext.product.itemName}
                                boxSize="60px"
                                objectFit="cover"
                                borderRadius="md"
                            />
                            <VStack align="start" flex="1" gap={1}>
                                <Text fontWeight="semibold" fontSize="sm" lineHeight="short">
                                    {checkoutContext.product.itemName}
                                </Text>
                                <HStack gap={2}>
                                    <Badge colorScheme="green" variant="solid">
                                        🏆 Winning Bid
                                    </Badge>
                                    <Text fontSize="sm" fontWeight="bold" color="green.600">
                                        {formatPrice(convertPrice(checkoutContext.winningBid.amount, 'USD'))}
                                    </Text>
                                </HStack>
                                <Text fontSize="xs" color="gray.600">
                                    Auction ended: {new Date(checkoutContext.auctionEndDate).toLocaleDateString()}
                                </Text>
                            </VStack>
                        </HStack>
                    </Box>
                )
        }
    }

    return (
        <Card.Root
            position={'sticky'}
            top={20}
            width="100%"
            boxShadow="md"
            borderRadius="md"
            bg="white"
            border="1px solid"
            borderColor="gray.200"
            zIndex={10}
        >
            <Card.Header>
                <Heading size="lg">Order Summary</Heading>
                <Text color="gray.600" fontSize="sm">
                    {totals.itemCount} item{totals.itemCount !== 1 ? 's' : ''}
                </Text>
            </Card.Header>

            <Card.Body>
                <VStack gap={4} align="stretch">
                    {/* Items */}
                    <VStack gap={3} align="stretch">
                        {renderItems()}
                    </VStack>

                    <Separator />

                    {/* Price Breakdown */}
                    <VStack gap={3} align="stretch">
                        <HStack justify="space-between">
                            <Text color="gray.600">Subtotal</Text>
                            <Text fontWeight="semibold">{formatPrice(totals.subtotal)}</Text>
                        </HStack>

                        <HStack justify="space-between">
                            <Text color="gray.600">Shipping</Text>
                            <Text fontWeight="semibold">{formatPrice(totals.shippingCost)}</Text>
                        </HStack>

                        <HStack justify="space-between">
                            <Text color="gray.600">Tax (10%)</Text>
                            <Text fontWeight="semibold">{formatPrice(totals.tax)}</Text>
                        </HStack>

                        <Separator />

                        <HStack justify="space-between">
                            <Text fontSize="lg" fontWeight="bold">Total</Text>
                            <Text fontSize="lg" fontWeight="bold" color="blue.600">
                                {formatPrice(totals.total)}
                            </Text>
                        </HStack>
                    </VStack>

                    {/* Additional Info */}
                    {checkoutContext.type === 'bidding' && (
                        <Box p={3} bg="blue.50" borderRadius="md" border="1px solid" borderColor="blue.200">
                            <Text fontSize="sm" color="blue.700" fontWeight="semibold">
                                💡 Payment Reminder
                            </Text>
                            <Text fontSize="xs" color="blue.600" mt={1}>
                                Complete payment within {checkoutContext.hoursRemaining} hours to secure your auction win.
                            </Text>
                        </Box>
                    )}

                    {checkoutContext.type === 'cart' && (
                        <Box p={3} bg="gray.50" borderRadius="md" border="1px solid" borderColor="gray.200">
                            <Text fontSize="sm" color="gray.700" fontWeight="semibold">
                                📦 Shipping Info
                            </Text>
                            <Text fontSize="xs" color="gray.600" mt={1}>
                                Items will be shipped together. Estimated delivery: 3-7 business days.
                            </Text>
                        </Box>
                    )}
                </VStack>
            </Card.Body>

            <Box
                bg="white"
                borderTop="1px solid"
                borderColor="gray.200"
                p={4}
                zIndex={10}
            >
                <Button
                    size="lg"
                    width="full"
                    colorPalette="blue"
                    type='submit'
                    loading={isSubmitting}
                    loadingText="Processing..."
                >
                    <HStack gap={2} align="center">
                        <FaLock size={14} />
                        <Text>Complete Payment</Text>
                        <Text fontWeight="bold">
                            {formatPrice(totals.total)}
                        </Text>
                    </HStack>
                </Button>

                <Text fontSize="xs" color="gray.500" textAlign="center" mt={2}>
                    🔒 Your payment information is secure and encrypted
                </Text>
            </Box>
        </Card.Root>
    )
}

export default CheckoutSummary
