'use client'
import React, { useState } from 'react'
import {
    Box,
    Card,
    Heading,
    Text,
    VStack,
    HStack,
    Button,
    Badge,
    RadioGroup,
    Spinner,
    Input,
    Textarea,
    Grid,
    GridItem,
    Checkbox,
    Dialog,
} from '@chakra-ui/react'
import { FormControl, FormLabel, FormErrorMessage } from '@/components/ui/form'
import { useShippingAddressesQuery, useCreateShippingAddressMutation, ShippingAddress } from '@/services/useCheckoutQuery'
import { FaPlus, FaEdit, FaMapMarkerAlt, FaHome } from 'react-icons/fa'
import { useForm, Controller } from 'react-hook-form'
import FormSelectField, { SelectOption } from '@/components/ui/form/FormSelectField'
import countriesData from '@/data/countries.json'

interface AddressSelectorProps {
    onAddressSelect: (address: ShippingAddress) => void
    selectedAddressId?: string
}

const AddressSelector: React.FC<AddressSelectorProps> = ({
    onAddressSelect,
    selectedAddressId
}) => {
    const [isAddModalOpen, setIsAddModalOpen] = useState(false)
    const [selectedAddress, setSelectedAddress] = useState<string>(selectedAddressId || '')

    const { data: addresses, isLoading, error } = useShippingAddressesQuery()
    const createAddressMutation = useCreateShippingAddressMutation()

    const {
        control,
        handleSubmit,
        formState: { errors },
        reset
    } = useForm<ShippingAddress>({
        defaultValues: {
            country: 'Indonesia',
            isDefault: false
        }
    })

    // Convert countries to select options
    const countryOptions: SelectOption[] = countriesData.map((country: { code: string; name: string }) => ({
        value: country.name,
        label: country.name
    }))

    const handleAddressSelection = (addressId: string) => {
        setSelectedAddress(addressId)
        const address = addresses?.find(addr => addr.id === addressId)
        if (address) {
            onAddressSelect(address)
        }
    }

    const handleAddNewAddress = async (data: ShippingAddress) => {
        try {
            const newAddress = await createAddressMutation.mutateAsync(data)
            setIsAddModalOpen(false)
            reset()
            // Auto-select the new address
            if (newAddress.id) {
                handleAddressSelection(newAddress.id)
            }
        } catch (error) {
            console.error('Failed to create address:', error)
        }
    }

    if (isLoading) {
        return (
            <Card.Root>
                <Card.Header>
                    <Heading size="md">Shipping Address</Heading>
                </Card.Header>
                <Card.Body>
                    <HStack>
                        <Spinner size="sm" />
                        <Text>Loading addresses...</Text>
                    </HStack>
                </Card.Body>
            </Card.Root>
        )
    }

    if (error) {
        return (
            <Card.Root>
                <Card.Header>
                    <Heading size="md">Shipping Address</Heading>
                </Card.Header>
                <Card.Body>
                    <Text color="red.500">Failed to load addresses</Text>
                </Card.Body>
            </Card.Root>
        )
    }

    return (
        <>
            <Card.Root>
                <Card.Header>
                    <HStack justify="space-between" align="center">
                        <VStack align="start" gap={1}>
                            <Heading size="md">Shipping Address</Heading>
                            <Text fontSize="sm" color="gray.600">
                                Choose an existing address or add a new one
                            </Text>
                        </VStack>
                        <Button
                            size="sm"
                            colorPalette="blue"
                            variant="outline"
                            onClick={() => setIsAddModalOpen(true)}
                        >
                            <FaPlus size={12} />
                            Add New
                        </Button>
                    </HStack>
                </Card.Header>

                <Card.Body>
                    <VStack gap={4} align="stretch">
                        {addresses && addresses.length > 0 ? (
                            <RadioGroup.Root
                                value={selectedAddress}
                                onValueChange={(value) => handleAddressSelection(value?.value ?? '')}
                            >
                                <VStack gap={3} align="stretch">
                                    {addresses.map((address) => (
                                        <Box
                                            key={address.id}
                                            border="2px solid"
                                            borderColor={selectedAddress === address.id ? 'blue.500' : 'gray.200'}
                                            borderRadius="lg"
                                            p={4}
                                            bg={selectedAddress === address.id ? 'blue.50' : 'white'}
                                            cursor="pointer"
                                            transition="all 0.2s"
                                            _hover={{ borderColor: 'blue.300' }}
                                            onClick={() => handleAddressSelection(address.id!)}
                                        >
                                            <HStack gap={3} align="start">
                                                <RadioGroup.Item value={address.id!} mt={1}>
                                                    <RadioGroup.ItemHiddenInput />
                                                    <RadioGroup.ItemControl />
                                                </RadioGroup.Item>

                                                <VStack align="start" flex="1" gap={2}>
                                                    <HStack gap={2} align="center">
                                                        <FaMapMarkerAlt color="gray" size={14} />
                                                        <Text fontWeight="bold" fontSize="sm">
                                                            {address.name}
                                                        </Text>
                                                        {address.isDefault && (
                                                            <Badge colorScheme="green" size="sm">
                                                                <FaHome size={10} />
                                                                Default
                                                            </Badge>
                                                        )}
                                                    </HStack>

                                                    <Text fontSize="sm" color="gray.700" lineHeight="short">
                                                        {address.fullAddress}
                                                    </Text>

                                                    <Text fontSize="sm" color="gray.600">
                                                        {address.city}, {address.province} {address.postalCode}
                                                    </Text>

                                                    <Text fontSize="sm" color="gray.600">
                                                        {address.country}
                                                    </Text>
                                                </VStack>

                                                <Button
                                                    size="xs"
                                                    variant="ghost"
                                                    colorPalette="gray"
                                                >
                                                    <FaEdit size={12} />
                                                </Button>
                                            </HStack>
                                        </Box>
                                    ))}
                                </VStack>
                            </RadioGroup.Root>
                        ) : (
                            <Box textAlign="center" py={8}>
                                <VStack gap={3}>
                                    <FaMapMarkerAlt size={32} color="gray" />
                                    <Text color="gray.600">No addresses found</Text>
                                    <Text fontSize="sm" color="gray.500">
                                        Add your first shipping address to continue
                                    </Text>
                                    <Button
                                        colorPalette="blue"
                                        onClick={() => setIsAddModalOpen(true)}
                                    >
                                        <FaPlus size={12} />
                                        Add Address
                                    </Button>
                                </VStack>
                            </Box>
                        )}
                    </VStack>
                </Card.Body>
            </Card.Root>

            {/* Add Address Modal */}
            <Dialog.Root open={isAddModalOpen} onOpenChange={(e) => setIsAddModalOpen(e.open)}>
                <Dialog.Backdrop />
                <Dialog.Positioner>
                    <Dialog.Content maxW="2xl">
                        <Dialog.Header>
                            <Dialog.Title>Add New Shipping Address</Dialog.Title>
                        </Dialog.Header>

                        <Dialog.Body>
                            <form onSubmit={handleSubmit(handleAddNewAddress)}>
                                <VStack gap={4} align="stretch">
                                    <FormControl isInvalid={!!errors.name}>
                                        <FormLabel>Full Name</FormLabel>
                                        <Controller
                                            name="name"
                                            control={control}
                                            rules={{ required: 'Full name is required' }}
                                            render={({ field }) => (
                                                <Input
                                                    {...field}
                                                    placeholder="Enter recipient's full name"
                                                />
                                            )}
                                        />
                                        <FormErrorMessage>
                                            {errors.name?.message}
                                        </FormErrorMessage>
                                    </FormControl>

                                    <FormControl isInvalid={!!errors.fullAddress}>
                                        <FormLabel>Full Address</FormLabel>
                                        <Controller
                                            name="fullAddress"
                                            control={control}
                                            rules={{ required: 'Full address is required' }}
                                            render={({ field }) => (
                                                <Textarea
                                                    {...field}
                                                    placeholder="Enter complete address"
                                                    rows={3}
                                                />
                                            )}
                                        />
                                        <FormErrorMessage>
                                            {errors.fullAddress?.message}
                                        </FormErrorMessage>
                                    </FormControl>

                                    <Grid templateColumns={{ base: '1fr', md: '1fr 1fr' }} gap={4}>
                                        <GridItem>
                                            <FormControl isInvalid={!!errors.city}>
                                                <FormLabel>City</FormLabel>
                                                <Controller
                                                    name="city"
                                                    control={control}
                                                    rules={{ required: 'City is required' }}
                                                    render={({ field }) => (
                                                        <Input
                                                            {...field}
                                                            placeholder="Enter city"
                                                        />
                                                    )}
                                                />
                                                <FormErrorMessage>
                                                    {errors.city?.message}
                                                </FormErrorMessage>
                                            </FormControl>
                                        </GridItem>

                                        <GridItem>
                                            <FormControl isInvalid={!!errors.province}>
                                                <FormLabel>Province</FormLabel>
                                                <Controller
                                                    name="province"
                                                    control={control}
                                                    rules={{ required: 'Province is required' }}
                                                    render={({ field }) => (
                                                        <Input
                                                            {...field}
                                                            placeholder="Enter province"
                                                        />
                                                    )}
                                                />
                                                <FormErrorMessage>
                                                    {errors.province?.message}
                                                </FormErrorMessage>
                                            </FormControl>
                                        </GridItem>
                                    </Grid>

                                    <Grid templateColumns={{ base: '1fr', md: '1fr 1fr' }} gap={4}>
                                        <GridItem>
                                            <FormControl isInvalid={!!errors.postalCode}>
                                                <FormLabel>Postal Code</FormLabel>
                                                <Controller
                                                    name="postalCode"
                                                    control={control}
                                                    rules={{ required: 'Postal code is required' }}
                                                    render={({ field }) => (
                                                        <Input
                                                            {...field}
                                                            placeholder="Enter postal code"
                                                        />
                                                    )}
                                                />
                                                <FormErrorMessage>
                                                    {errors.postalCode?.message}
                                                </FormErrorMessage>
                                            </FormControl>
                                        </GridItem>

                                        <GridItem>
                                            <FormControl isInvalid={!!errors.country}>
                                                <FormLabel>Country</FormLabel>
                                                <Controller
                                                    name="country"
                                                    control={control}
                                                    rules={{ required: 'Country is required' }}
                                                    render={({ field }) => (
                                                        <FormSelectField
                                                            {...field}
                                                            options={countryOptions}
                                                            placeholder="Select country"
                                                            value={countryOptions.find(option => option.value === field.value)}
                                                        />
                                                    )}
                                                />
                                                <FormErrorMessage>
                                                    {errors.country?.message}
                                                </FormErrorMessage>
                                            </FormControl>
                                        </GridItem>
                                    </Grid>

                                    <FormControl>
                                        <Controller
                                            name="isDefault"
                                            control={control}
                                            render={({ field: { value, onChange } }) => (
                                                <Checkbox.Root
                                                    checked={value}
                                                    onCheckedChange={(value) => onChange(Boolean(value.checked))}
                                                >
                                                    <Checkbox.HiddenInput />
                                                    <Checkbox.Control />
                                                    <Checkbox.Label>Set as default address</Checkbox.Label>
                                                </Checkbox.Root>
                                            )}
                                        />
                                    </FormControl>
                                </VStack>
                            </form>
                        </Dialog.Body>

                        <Dialog.Footer>
                            <HStack gap={3}>
                                <Button
                                    variant="outline"
                                    onClick={() => {
                                        setIsAddModalOpen(false)
                                        reset()
                                    }}
                                >
                                    Cancel
                                </Button>
                                <Button
                                    colorPalette="blue"
                                    onClick={handleSubmit(handleAddNewAddress)}
                                    loading={createAddressMutation.isPending}
                                    loadingText="Adding..."
                                >
                                    Add Address
                                </Button>
                            </HStack>
                        </Dialog.Footer>
                    </Dialog.Content>
                </Dialog.Positioner>
            </Dialog.Root>
        </>
    )
}

export default AddressSelector
