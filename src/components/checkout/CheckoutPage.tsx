'use client'
import React, { useState } from 'react'
import { useSearchParams, useRouter } from 'next/navigation'
import { useSession } from 'next-auth/react'
import {
    Box,
    Button,
    Heading,
    Text,
    VStack,
    Spinner,
    Container
} from '@chakra-ui/react'
import { Alert } from '@/components/ui/alert'

import {
    useCartCheckoutValidation,
    useBuyNowCheckoutValidation,
    useBiddingCheckoutValidation,
    getCheckoutErrorMessage,
    isRecoverableError
} from '@/hooks/useCheckoutValidation'
import {
    CheckoutType,
    CheckoutFormData,
    useBuyNowMutation,
    useCreatePaymentMutation,
    useCreateInvoiceMutation
} from '@/services/useCheckoutQuery'
import CheckoutForm from './CheckoutForm'
import CheckoutSummary from './CheckoutSummary'
import AuctionWinnerBanner from './AuctionWinnerBanner'
import { FormProvider, useForm } from 'react-hook-form'
import { useCurrencyLanguage } from '@/contexts/CurrencyLanguageContext'
import { toaster } from '@/components/ui/toaster'

const CheckoutPage = () => {
    const router = useRouter()
    const searchParams = useSearchParams()
    const { status, data: session } = useSession()
    const { currency } = useCurrencyLanguage();

    // Extract checkout parameters from URL
    const productId = searchParams.get('productId')
    const bidId = searchParams.get('bidId')
    const type = searchParams.get('type')

    // Determine checkout type based on parameters
    const checkoutType: CheckoutType = (() => {
        if (type === 'bidding' && productId) return 'bidding'
        if (productId && !type) return 'buy-now'
        return 'cart'
    })()

    // State management
    const [isSubmitting, setIsSubmitting] = useState(false)
    const [checkoutError, setCheckoutError] = useState<string | null>(null)
    const [checkoutStep, setCheckoutStep] = useState<'form' | 'processing' | 'payment' | 'success'>('form')

    // Mutations
    const buyNowMutation = useBuyNowMutation()
    const createPaymentMutation = useCreatePaymentMutation()
    const createInvoiceMutation = useCreateInvoiceMutation()

    // Validation hooks based on checkout type
    const cartValidation = useCartCheckoutValidation(checkoutType === 'cart')
    const buyNowValidation = useBuyNowCheckoutValidation(
        productId || '',
        checkoutType === 'buy-now'
    )
    const biddingValidation = useBiddingCheckoutValidation(
        productId || '',
        bidId || undefined,
        checkoutType === 'bidding'
    )

    const method = useForm<CheckoutFormData>({
        defaultValues: {
            currency: currency,
            country: 'Indonesia',
            paymentMethod: 'xendit_invoice'
        }
    })

    // Get current validation based on checkout type
    const currentValidation = (() => {
        switch (checkoutType) {
            case 'cart': return cartValidation
            case 'buy-now': return buyNowValidation
            case 'bidding': return biddingValidation
            default: return cartValidation
        }
    })()

    const { data: checkoutContext, isLoading, error, isValid, refetch } = currentValidation

    if (status === 'loading') {
        return (
            <Container maxW="container.xl" py={8}>
                <VStack gap={4} align="center">
                    <Spinner size="lg" />
                    <Text>Loading session...</Text>
                </VStack>
            </Container>
        );
    }

    if (status === 'unauthenticated') {
        return (
            <Container maxW="container.xl" py={8}>
                <Alert status="warning">
                    <Alert.Icon status="warning" />
                    <Alert.Title>Authentication Required</Alert.Title>
                    <Alert.Description>
                        Please log in to access the checkout page.
                    </Alert.Description>
                </Alert>
            </Container>
        );
    }

    // Handle validation errors
    const handleRetry = () => {
        refetch()
    }

    if (isLoading) {
        return (
            <Box minH="50vh" display="flex" alignItems="center" justifyContent="center">
                <VStack gap={4}>
                    <Spinner size="xl" color="blue.500" />
                    <Text>Loading checkout...</Text>
                </VStack>
            </Box>
        )
    }

    // Validation error state - handle both explicit errors and failed validation
    if ((error && !isValid) || (!isLoading && !isValid && !error)) {
        const errorMessage = error ? getCheckoutErrorMessage(error) : 'Checkout validation failed. Please check your request and try again.'
        const canRetry = error ? isRecoverableError(error) : true

        return (
            <Box minH="50vh" display="flex" alignItems="center" justifyContent="center">
                <Alert status="error" maxW="lg">
                    <Alert.Icon status="error" />
                    <VStack align="start" flex="1">
                        <Alert.Title>Checkout Not Available</Alert.Title>
                        <Alert.Description>
                            {errorMessage}
                        </Alert.Description>
                        {canRetry && (
                            <Button
                                mt={3}
                                size="sm"
                                colorScheme="red"
                                variant="outline"
                                onClick={handleRetry}
                            >
                                Try Again
                            </Button>
                        )}
                    </VStack>
                </Alert>
            </Box>
        )
    }

    if (isValid && checkoutContext) {
        const onSubmit = async (data: CheckoutFormData) => {
            if (!checkoutContext) {
                toaster.create({
                    title: "Error",
                    description: "Checkout context not found",
                    type: "error",
                })
                return
            }

            setIsSubmitting(true)
            setCheckoutError(null)
            setCheckoutStep('processing')

            // Enhanced validation for different checkout types
            const isBiddingCheckout = checkoutType === 'bidding'
            const isBuyNowCheckout = checkoutType === 'buy-now'
            const isCartCheckout = checkoutType === 'cart'

            if (isBiddingCheckout && checkoutContext.type === 'bidding') {
                // Validate that user is the winning bidder
                const winnerUserId = checkoutContext.winningBid?.bidderId
                if (!checkoutContext.winningBid || winnerUserId !== session?.user?.id) {
                    toaster.create({
                        title: "Error",
                        description: "You are not the winning bidder for this auction",
                        type: "error",
                    })
                    setIsSubmitting(false)
                    return
                }
            }

            try {
                // Enhanced order creation for all checkout types
                const orderData = {
                    currency: data.currency,
                    shippingAddress: {
                        name: data.name,
                        address: data.address,
                        city: data.city,
                        provinceRegion: data.provinceRegion,
                        zipCode: data.zipCode,
                        country: data.country,
                    },
                    paymentMethod: {
                        type: data.paymentMethod,
                        method: data.selectedPaymentMethod,
                        channel: data.paymentChannel,
                        ewalletType: data.ewalletType,
                        bankCode: data.bankCode,
                        retailOutletName: data.retailOutletName,
                    },
                    notes: data.notes,
                    // Add specific information based on checkout type
                    ...(isBiddingCheckout && checkoutContext.type === 'bidding' && {
                        bidId: checkoutContext.winningBid?.id || '',
                        orderType: 'bidding',
                        productId: checkoutContext.product.id,
                        winningBid: checkoutContext.winningBid?.amount || 0
                    }),
                    ...(isCartCheckout && checkoutContext.type === 'cart' && {
                        orderType: 'cart',
                        products: checkoutContext.items.map(item => item.id),
                        cartItems: checkoutContext.items.map(item => ({
                            productId: item.productId,
                            quantity: item.quantity,
                            price: parseInt(String(item.price), 10),
                        }))
                    }),
                    ...(isBuyNowCheckout && checkoutContext.type === 'buy-now' && {
                        orderType: 'buy-now',
                        products: [checkoutContext.product.id],
                        productId: checkoutContext.product.id,
                        quantity: checkoutContext.quantity
                    })
                }

                const order = await buyNowMutation.mutateAsync(orderData)

                // Step 2: Create payment based on selected method type
                setCheckoutStep('payment')
                const basePaymentData = {
                    orderId: order.id,
                    currency: data.currency,
                    customerEmail: session?.user?.email || '',
                    customerName: `${session?.user?.firstName || ''} ${session?.user?.lastName || ''}`.trim(),
                    description: `Payment for Order #${order.orderNumber}`,
                    successRedirectUrl: isBiddingCheckout
                        ? `${window.location.origin}/account/bidding?payment=success&orderId=${order.id}`
                        : `${window.location.origin}/order-tracking/${order.id}?payment=success`,
                    failureRedirectUrl: isBiddingCheckout
                        ? `${window.location.origin}/account/bidding?payment=failed&orderId=${order.id}`
                        : `${window.location.origin}/order-tracking/${order.id}?payment=failed`,
                }

                let paymentResult;

                if (data.paymentMethod === 'ewallet' && data.ewalletType) {
                    // Create eWallet payment
                    paymentResult = await createPaymentMutation.mutateAsync({
                        ...basePaymentData,
                        paymentType: 'ewallet',
                        ewalletType: data.ewalletType,
                        customerPhone: session?.user?.phoneNumber || '',
                    })

                } else if (data.paymentMethod === 'virtual_account' && data.bankCode) {
                    // Create Virtual Account payment
                    paymentResult = await createPaymentMutation.mutateAsync({
                        ...basePaymentData,
                        paymentType: 'virtual_account',
                        bankCode: data.bankCode,
                    })

                } else if (data.paymentMethod === 'retail_outlet' && data.retailOutletName) {
                    // Create Retail Outlet payment
                    paymentResult = await createPaymentMutation.mutateAsync({
                        ...basePaymentData,
                        paymentType: 'retail_outlet',
                        retailOutletName: data.retailOutletName,
                    })

                } else {
                    // Default to invoice for all other payment methods
                    paymentResult = await createInvoiceMutation.mutateAsync(basePaymentData)
                }

                // Step 3: Success
                setCheckoutStep('success')

                toaster.create({
                    title: isBiddingCheckout ? "Auction Payment Processed" : "Order Created",
                    description: isBiddingCheckout
                        ? `Auction payment for Order #${order.orderNumber} processed. Redirecting to order tracking...`
                        : `Order #${order.orderNumber} created. Redirecting to payment...`,
                    type: "success",
                })

                if ((paymentResult as any)?.invoiceUrl || (paymentResult as any)?.paymentUrl) {
                    window.location.href = (paymentResult as any).invoiceUrl || (paymentResult as any).paymentUrl
                    return
                }

                setTimeout(() => {
                    if (isBiddingCheckout) {
                        router.push(`/account/bidding?orderId=${order.id}`)
                    } else {
                        router.push(`/order-tracking/${order.id}`)
                    }
                }, 1000)
            } catch (error: any) {
                console.error(`${checkoutType} checkout failed:`, error)
                setCheckoutStep('form') // Reset to form on error

                let title = "Order Creation Failed"
                let description = "Failed to create order. Please try again."

                if (isBiddingCheckout) {
                    title = "Auction Payment Failed"
                    description = "Failed to process auction payment. Please try again."
                } else if (isCartCheckout) {
                    title = "Cart Checkout Failed"
                    description = "Failed to process cart checkout. Please try again."
                } else if (isBuyNowCheckout) {
                    title = "Buy Now Failed"
                    description = "Failed to process buy now order. Please try again."
                }

                setCheckoutError(description)
                toaster.create({
                    title,
                    description,
                    type: "error",
                    duration: 5000,
                })
            } finally {
                setIsSubmitting(false)
            }
        }

        return (
            <Box maxW="7xl" mx="auto" p={6} position="relative">
                {/* Processing Overlay */}
                {checkoutStep !== 'form' && (
                    <Box
                        position="fixed"
                        top={0}
                        left={0}
                        right={0}
                        bottom={0}
                        bg="blackAlpha.600"
                        zIndex={9999}
                        display="flex"
                        alignItems="center"
                        justifyContent="center"
                    >
                        <Box
                            bg="white"
                            p={8}
                            borderRadius="lg"
                            boxShadow="xl"
                            textAlign="center"
                            maxW="md"
                        >
                            <VStack gap={4}>
                                <Spinner size="xl" color="blue.500" />
                                <Heading size="md">
                                    {checkoutStep === 'processing' && 'Creating Your Order...'}
                                    {checkoutStep === 'payment' && 'Setting Up Payment...'}
                                    {checkoutStep === 'success' && 'Order Created Successfully!'}
                                </Heading>
                                <Text color="gray.600">
                                    {checkoutStep === 'processing' && 'Please wait while we process your order.'}
                                    {checkoutStep === 'payment' && 'Preparing your payment gateway.'}
                                    {checkoutStep === 'success' && 'Redirecting to your order details...'}
                                </Text>
                            </VStack>
                        </Box>
                    </Box>
                )}

                <VStack gap={8} align="stretch">
                    <Box>
                        <Heading size="xl" mb={2}>Checkout</Heading>
                        <Text color="gray.600">
                            Complete your {checkoutType === 'cart' ? 'cart' : checkoutType === 'buy-now' ? 'purchase' : 'auction payment'}
                        </Text>
                    </Box>

                    {checkoutType === 'bidding' && checkoutContext.type === 'bidding' && (
                        <AuctionWinnerBanner
                            bidData={checkoutContext.winningBid}
                            product={checkoutContext.product}
                            paymentDeadline={checkoutContext.paymentDeadline}
                            hoursRemaining={checkoutContext.hoursRemaining}
                        />
                    )}

                    {checkoutError && (
                        <Alert status="error">
                            <Alert.Icon status="error" />
                            <VStack align="start" flex="1">
                                <Alert.Title>Checkout Error</Alert.Title>
                                <Alert.Description>
                                    {checkoutError}
                                </Alert.Description>
                                <Button
                                    mt={2}
                                    size="sm"
                                    colorScheme="red"
                                    variant="outline"
                                    onClick={() => setCheckoutError(null)}
                                >
                                    Dismiss
                                </Button>
                            </VStack>
                        </Alert>
                    )}

                    <FormProvider {...method}>
                        <form onSubmit={method.handleSubmit(onSubmit)}>
                            <Box display="grid" gridTemplateColumns={{ base: '1fr', lg: '2fr 1fr' }} gap={6}>
                                <Box>
                                    <CheckoutForm
                                        checkoutContext={checkoutContext}
                                    />
                                </Box>

                                <Box>
                                    <CheckoutSummary
                                        checkoutContext={checkoutContext}
                                        isSubmitting={isSubmitting}
                                    />
                                </Box>
                            </Box>
                        </form>
                    </FormProvider>
                </VStack>
            </Box>
        )
    }

    // Fallback loading state
    return (
        <Box minH="50vh" display="flex" alignItems="center" justifyContent="center">
            <VStack gap={4}>
                <Spinner size="xl" color="blue.500" />
                <Text>Preparing checkout...</Text>
            </VStack>
        </Box>
    )
}

export default CheckoutPage
