"use client"
import React, { useState, useEffect, useMemo, useCallback } from 'react'
import {
    Box,
    Button,
    Text,
    VStack,
    HS<PERSON>ck,
    Badge,
    Flex,
    createListCollection,
    Icon,
    Skeleton,
} from '@chakra-ui/react'
import { Fa<PERSON>lock, FaHistory, FaSyncAlt } from 'react-icons/fa'
import { useCurrencyLanguage } from '@/contexts/CurrencyLanguageContext'
import {
    usePlaceBidMutation,
    useUserBidQuery,
    useBidHistoryQuery,
    useAutoBidMutation,
    useCancelAutoBidMutation,
    useUserAutoBidQuery
} from '@/services/useBiddingQuery'
import ClientOnly from '@/components/ui/ClientOnly'
import { ChakraSelect } from '../ui/select/ChakraSelect'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import { useTranslations } from 'next-intl'
import { toaster } from '@/components/ui/toaster'
import { useProductWebSocket } from '@/hooks/useProductWebSocket'
import { useQueryClient } from '@tanstack/react-query'

interface AuctionInfoProps {
    currentBid: number;
    startingPrice: number;
    bidCount: number;
    auctionStartDate: string;
    auctionEndDate: string;
    extendedBiddingEnabled?: boolean;
    extendedBiddingMinutes?: number;
    extendedBiddingDuration?: number;
    productId: string;
    productName: string;
    onPlaceBid?: (productId: string, bidAmount: number) => void;
    onAddToWishlist?: (productId: string) => void;
    isInWishlist?: boolean;
    onShowBidHistory?: (productId: string) => void;
    mb?: number;
    isLoadingAuth?: boolean;
    isAuthenticated?: boolean;
    auctionStatus?: 'active' | 'ended' | 'upcoming'; // Added auctionStatus prop
    timeLeft?: string; // Added timeLeft prop for auction status
}

const AuctionInfo: React.FC<AuctionInfoProps> = ({
    currentBid,
    startingPrice,
    bidCount,
    auctionEndDate,
    extendedBiddingEnabled = false,
    extendedBiddingMinutes,
    extendedBiddingDuration,
    productId,
    productName,
    onShowBidHistory,
    isLoadingAuth = false,
    isAuthenticated = false,
    auctionStatus = 'active',
    timeLeft = 'N/A',
    mb = 0
}) => {
    const router = useRouter();
    const dataSession = useSession()
    const t = useTranslations()
    const { formatPrice, convertPrice, currency } = useCurrencyLanguage()

    // Real-time bid state
    const [realtimeBid, setRealtimeBid] = useState(currentBid)
    const [realtimeBidCount, setRealtimeBidCount] = useState(bidCount)
    const [lastBidder, setLastBidder] = useState<string | null>(null)
    const [realtimeEndTime, setRealtimeEndTime] = useState(auctionEndDate)
    const queryClient = useQueryClient();

    // Memoized WebSocket callbacks to prevent re-renders
    const onBidUpdate = useCallback((data: any) => {
        console.log('📈 Real-time bid update received:', data)

        // Batch state updates to prevent multiple re-renders
        if (data.currentBid) {
            setRealtimeBid(data.currentBid)
            setIsUpdating(true)
            // Reset animation after 2 seconds
            setTimeout(() => setIsUpdating(false), 2000)
        }
        if (data.bidCount !== undefined) {
            setRealtimeBidCount(data.bidCount)
        }
        if (data.bidderName) {
            setLastBidder(data.bidderName)
        }

        if (data.bidderName && data.bidderName !== dataSession.data?.user?.name) {
            toaster.create({
                title: t('Auction.newBidReceived'),
                description: `${data.bidderName} ${t('Auction.placedBidOf', {
                    amount: formatPrice(convertPrice(data.currentBid, 'USD'))
                })}`,
                type: 'info',
                duration: 3000,
            })
        }

        // Handle auction extension notifications
        if (data.type === 'broadcast' && data.data?.type === 'auction_extended') {
            console.log('Auction extended, invalidating product queries');
            toaster.create({
                title: 'Auction Extended',
                description: `Auction for ${productName} has been extended.`,
                type: "info",
                duration: 5000,
            });
        }

        // Handle new bid notifications
        if (data.type === 'broadcast' && data.data?.type === 'new_bid') {
            console.log('New bid received, invalidating queries');
            toaster.create({
                title: 'New Bid',
                description: `New bid of ${data.data.bidAmount}`,
                type: "info",
                duration: 5000,
            });
        }

        queryClient.invalidateQueries({
            queryKey: ['bidding', 'history', productId]
        });
        queryClient.invalidateQueries({
            queryKey: ['bidding', 'user-bid', productId]
        });
        queryClient.invalidateQueries({
            queryKey: ['auction-extensions', 'product', productId]
        });
        queryClient.invalidateQueries({
            queryKey: ['auction-extensions', 'stats', productId]
        });
    }, [dataSession.data?.user?.name, t, formatPrice, convertPrice])

    const onAuctionExtended = useCallback((data: any) => {
        console.log('⏰ Auction extension received:', data)

        if (data.newEndTime) {
            setRealtimeEndTime(data.newEndTime)
            toaster.create({
                title: t('auction.auctionExtended'),
                description: t('auction.auctionExtendedDescription'),
                type: 'warning',
                duration: 5000,
            })
        }
    }, [t])

    const onAuctionEnded = useCallback((data: any) => {
        console.log('🏁 Auction ended:', data)

        toaster.create({
            title: t('auction.auctionEnded'),
            description: t('auction.auctionEndedDescription'),
            type: 'success',
            duration: 5000,
        })
    }, [t])

    const onError = useCallback((error: any) => {
        console.error('❌ Product WebSocket error:', error)
    }, [])

    // Optimized WebSocket connection for this product
    const {
        isConnected,
        connectionQuality,
        subscriptionHealth
    } = useProductWebSocket({
        productId: productId || '',
        onBidUpdate,
        onAuctionExtended,
        onAuctionEnded,
        onError,
        enableAutoReconnect: true,
        maxRetries: 5,
        retryDelay: 2000
    })
    const [isUpdating, setIsUpdating] = useState(false)

    // Regular bidding state
    const [bidAmount, setBidAmount] = useState(currentBid + 1);

    // Auto-bid state
    const [autoBidEnabled, setAutoBidEnabled] = useState(false);
    const [startingBid, setStartingBid] = useState(realtimeBid + 1); // Starting bid amount
    const [maxBudget, setMaxBudget] = useState(realtimeBid + 100);
    const [bidIncrement, setBidIncrement] = useState(5);

    // Bidding mode state
    const [biddingMode, setBiddingMode] = useState<'bid_now' | 'auto_bid'>('bid_now');

    const placeBidMutation = usePlaceBidMutation();
    const autoBidMutation = useAutoBidMutation();
    const cancelAutoBidMutation = useCancelAutoBidMutation();
    const { data: userBid } = useUserBidQuery(productId, dataSession?.data?.user?.id || '');
    const { data: bidHistory } = useBidHistoryQuery(productId);
    const { data: userAutoBid } = useUserAutoBidQuery(productId);

    // Memoized connection status indicator
    const connectionStatusColor = useMemo(() => {
        switch (connectionQuality) {
            case 'excellent': return 'green'
            case 'good': return 'yellow'
            case 'poor': return 'red'
            default: return 'gray'
        }
    }, [connectionQuality])

    // Optimized state updates when props change
    useEffect(() => {
        setRealtimeBid(prev => prev !== currentBid ? currentBid : prev)
        setRealtimeBidCount(prev => prev !== bidCount ? bidCount : prev)
    }, [currentBid, bidCount])

    // Memoized calculations to prevent unnecessary recalculations
    const minBidIncrement = useMemo(() =>
        Math.max(1, Math.floor(realtimeBid * 0.05)), [realtimeBid]
    )
    const minBidAmount = useMemo(() =>
        realtimeBid + minBidIncrement, [realtimeBid, minBidIncrement]
    )

    useEffect(() => {
        setBidAmount(prev => prev !== minBidAmount ? minBidAmount : prev);
    }, [minBidAmount]);

    // Optimized auto-bid state synchronization
    useEffect(() => {
        if (userAutoBid) {
            setAutoBidEnabled(prev => prev !== userAutoBid.isActive ? userAutoBid.isActive : prev);
            setMaxBudget(prev => prev !== userAutoBid.maxBudget ? userAutoBid.maxBudget : prev);
            setBidIncrement(prev => prev !== userAutoBid.bidIncrement ? userAutoBid.bidIncrement : prev);

            // If auto bid is active, switch to auto_bid mode and disable bid_now
            if (userAutoBid.isActive) {
                setBiddingMode(prev => prev !== 'auto_bid' ? 'auto_bid' : prev);
            }
        }
    }, [userAutoBid]);

    // Check if user has placed any manual bids
    // const hasManualBids = userBid && !userAutoBid?.isActive;

    // Determine if bid now should be disabled
    // Only disable if auto bid is currently active (not just enabled in the past)
    const isBidNowDisabled = autoBidEnabled && (userAutoBid?.isActive);

    // Determine if auto bid should be disabled
    // Allow auto bid even if user has manual bids, but warn them
    // const isAutoBidDisabled = false; // Always allow auto bid activation



    // Generate optimized bid amount options (memoized for performance)
    const bidOptions = useMemo(() => {
        const options = [];
        let currentAmount = minBidAmount;
        const maxOptions = 50; // Limit to 50 options for better performance
        let optionCount = 0;

        // Add incremental options with smart limits
        while (currentAmount <= 1000000 && optionCount < maxOptions) {
            options.push(currentAmount);
            optionCount++;

            if (currentAmount < 100) {
                currentAmount += 5; // $5 increments under $100
            } else if (currentAmount < 1000) {
                currentAmount += 25; // $25 increments under $1,000
            } else if (currentAmount < 10000) {
                currentAmount += 100; // $100 increments under $10,000
            } else if (currentAmount < 100000) {
                currentAmount += 500; // $500 increments under $100,000
            } else {
                currentAmount += 1000; // $1,000 increments above $100,000
            }
        }

        // Add some high-value options if we haven't reached the limit
        if (optionCount < maxOptions) {
            const highValueOptions = [50000, 100000, 250000, 500000, 1000000];
            for (const value of highValueOptions) {
                if (value > currentAmount && optionCount < maxOptions) {
                    options.push(value);
                    optionCount++;
                }
            }
        }

        return options;
    }, [minBidAmount]); // Only recalculate when minBidAmount changes

    // Optimized bid options for auto-bid starting bid (filtered and memoized)
    const autoBidStartingBidOptions = useMemo(() => {
        return bidOptions.filter(amount => amount > currentBid);
    }, [bidOptions, currentBid]);

    // Optimized bid options for auto-bid max budget (filtered and memoized)
    const autoBidMaxBudgetOptions = useMemo(() => {
        return bidOptions.filter(amount => amount > startingBid);
    }, [bidOptions, startingBid]);

    // Memoized select collections for better performance
    const bidOptionsSelect = useMemo(() => createListCollection({
        items: bidOptions.map(amount => ({
            label: formatPrice(convertPrice(amount, 'USD'), currency), // Display in user's currency
            value: amount.toString() // Keep USD value for backend
        })),
    }), [bidOptions, currency, formatPrice, convertPrice]);

    const autoBidStartingBidSelect = useMemo(() => createListCollection({
        items: autoBidStartingBidOptions.map(amount => ({
            label: formatPrice(convertPrice(amount, 'USD'), currency),
            value: amount.toString()
        })),
    }), [autoBidStartingBidOptions, currency, formatPrice, convertPrice]);

    const autoBidMaxBudgetSelect = useMemo(() => createListCollection({
        items: autoBidMaxBudgetOptions.map(amount => ({
            label: formatPrice(convertPrice(amount, 'USD'), currency),
            value: amount.toString()
        })),
    }), [autoBidMaxBudgetOptions, currency, formatPrice, convertPrice]);

    // Optimized bid increment options (static, no need for dynamic generation)
    const bidIncrementSelect = useMemo(() => createListCollection({
        items: [1, 5, 10, 25, 50, 100].map(amount => ({
            label: formatPrice(convertPrice(amount, 'USD'), currency),
            value: amount.toString()
        })),
    }), [currency, formatPrice, convertPrice]);

    // Optimized value change handlers with useCallback
    const handleBidAmountChange = useCallback((value: any) => {
        if (!isBidNowDisabled) {
            console.log('Selected bid amount:', value);
            setBidAmount(value.value?.[0] ? parseFloat(value.value[0]) : 0);
        }
    }, [isBidNowDisabled]);

    const handleStartingBidChange = useCallback((value: any) => {
        const newStartingBid = value.value?.[0] ? parseFloat(value.value[0]) : startingBid;
        setStartingBid(newStartingBid);
        // Auto-adjust max budget if starting bid is higher
        if (newStartingBid >= maxBudget) {
            setMaxBudget(newStartingBid + 100);
        }
    }, [startingBid, maxBudget]);

    const handleMaxBudgetChange = useCallback((value: any) => {
        setMaxBudget(value.value?.[0] ? parseFloat(value.value[0]) : maxBudget);
    }, [maxBudget]);

    const handleBidIncrementChange = useCallback((value: any) => {
        setBidIncrement(value.value?.[0] ? parseFloat(value.value[0]) : bidIncrement);
    }, [bidIncrement]);

    const handlePlaceBid = useCallback(async () => {
        console.log('Placing bid:', bidAmount, 'USD for product:', productId);
        if (bidAmount >= minBidAmount) {
            try {
                // Send bid amount in USD to backend
                await placeBidMutation.mutateAsync({
                    productId,
                    bidAmount, // This is already in USD
                    bidType: 'manual'
                });
                // Reset bid amount to new minimum after successful bid
                setBidAmount(bidAmount + minBidIncrement);
            } catch (error) {
                console.error('Failed to place bid:', error);
            }
        }
    }, [bidAmount, minBidAmount, productId, placeBidMutation, minBidIncrement]);

    const handleEnableAutoBid = useCallback(async () => {
        // Validation
        if (startingBid <= currentBid) {
            toaster.create({
                title: "Invalid Starting Bid",
                description: `Starting bid must be higher than current bid (${formatPrice(currentBid)})`,
                type: "error",
            });
            return;
        }

        if (maxBudget <= startingBid) {
            toaster.create({
                title: "Invalid Max Budget",
                description: `Max budget must be higher than starting bid (${formatPrice(startingBid)})`,
                type: "error",
            });
            return;
        }

        if (bidIncrement <= 0) {
            toaster.create({
                title: "Invalid Bid Increment",
                description: 'Bid increment must be greater than 0',
                type: "error",
            });
            return;
        }

        if (bidIncrement >= (maxBudget - startingBid)) {
            toaster.create({
                title: "Invalid Bid Increment",
                description: 'Bid increment is too large for the budget range',
                type: "error",
            });
            return;
        }

        // const minIncrement = Math.max(1, Math.floor(currentBid * 0.01)); // 1% of current bid or $1 minimum
        // if (bidIncrement < minIncrement) {
        //     toaster.create({
        //         title: "Invalid Bid Increment",
        //         description: `Bid increment must be at least ${formatPrice(minIncrement, 'USD')}`,
        //         type: "error",
        //     });
        //     return;
        // }

        try {
            console.log('Enabling auto-bid with (USD):', { productId, startingBid, maxBudget, bidIncrement });
            // Send auto-bid settings in USD to backend
            const result = await autoBidMutation.mutateAsync({
                productId,
                startingBid, // Already in USD
                maxBudget, // Already in USD
                bidIncrement // Already in USD
            });
            console.log('Auto-bid enabled successfully:', result);
            setAutoBidEnabled(true);

            toaster.create({
                title: "Auto-bid Enabled",
                description: `Auto-bid activated with max budget of ${formatPrice(convertPrice(maxBudget, 'USD'), currency)}`,
                type: "success",
            });
        } catch (error) {
            console.error('Failed to enable auto-bid:', error);
            toaster.create({
                title: "Auto-bid Failed",
                description: error instanceof Error ? error.message : "Failed to enable auto-bidding",
                type: "error",
            });
        }
    }, [startingBid, currentBid, maxBudget, bidIncrement, productId, autoBidMutation, formatPrice, convertPrice, currency]);

    const handleDisableAutoBid = useCallback(async () => {
        try {
            await cancelAutoBidMutation.mutateAsync(productId);
            setAutoBidEnabled(false);

            // Allow user to switch back to manual bidding
            // Note: This does NOT cancel any bids already placed by auto-bid
            // User keeps their current bid position
            setBiddingMode('bid_now');

            toaster.create({
                title: "Auto-bid Disabled",
                description: "Auto-bidding has been disabled. Your current bids remain active.",
                type: "info",
            });
        } catch (error) {
            console.error('Failed to disable auto-bid:', error);
            toaster.create({
                title: "Error",
                description: "Failed to disable auto-bidding. Please try again.",
                type: "error",
            });
        }
    }, [productId, cancelAutoBidMutation, setBiddingMode]);



    const getStatusColor = () => {
        switch (auctionStatus) {
            case 'upcoming': return 'blue';
            case 'active': return 'green';
            case 'ended': return 'gray';
            default: return 'gray';
        }
    };

    const getStatusText = () => {
        switch (auctionStatus) {
            case 'upcoming': return 'Upcoming';
            case 'active': return 'Live Auction';
            case 'ended': return 'Auction Ended';
            default: return 'Unknown';
        }
    };

    return (
        <Box
            bg="white"
            p={6}
            borderRadius="lg"
            border="1px solid"
            borderColor="gray.200"
            mb={mb}
        >
            <VStack align="stretch" gap={4}>
                {/* Auction Status */}
                <Flex justify="space-between" align="center">
                    <Badge colorScheme={getStatusColor()} variant="solid" fontSize="sm" px={3} py={1}>
                        {getStatusText()}
                    </Badge>
                    <HStack>
                        <FaClock />
                        <ClientOnly fallback={<Text fontSize="sm" color="gray.600">Loading...</Text>}>
                            <Text fontSize="sm" color="gray.600">
                                {timeLeft}
                            </Text>
                        </ClientOnly>
                    </HStack>
                </Flex>
                <Box textAlign="center" py={6}>
                    <HStack justify="center" align="center" mb={3} gap={2}>
                        <Text fontSize="sm" color="gray.600" textTransform="uppercase" letterSpacing="wide">
                            {t('Product.currentBid')}
                        </Text>
                        <Box
                            w={2}
                            h={2}
                            borderRadius="full"
                            bg={'green'}
                            title={`Real-time: ${isConnected ? 'Connected' : 'Disconnected'} | Quality: ${connectionQuality} | Messages: ${subscriptionHealth.messagesReceived}`}
                        />
                        {isUpdating && (
                            <Text fontSize="xs" color="green.500" fontWeight="bold">
                                UPDATED
                            </Text>
                        )}
                    </HStack>

                    <Text
                        fontSize="5xl"
                        fontWeight="extrabold"
                        color={isUpdating ? "green.600" : "gray.800"}
                        lineHeight="1"
                        transition="color 0.3s ease"
                    >
                        {formatPrice(convertPrice(realtimeBid, 'USD'), currency)}
                    </Text>

                    <HStack justify="center" align="center" mt={3} gap={4}>
                        <Text fontSize="sm" color="gray.500">
                            {realtimeBidCount} {t('Product.bidCount')} • {t('Product.startingBid')}: {formatPrice(convertPrice(startingPrice, 'USD'), currency)}
                        </Text>
                        {bidHistory && bidHistory.bids.length > 0 && (
                            <Button
                                size="xs"
                                variant="ghost"
                                colorScheme="blue"
                                onClick={() => onShowBidHistory?.(productId)}
                            >
                                <FaHistory style={{ marginRight: '4px' }} />
                                History
                            </Button>
                        )}
                    </HStack>
                </Box>
                {(!bidHistory || bidHistory.bids.length === 0) && (
                    <Box
                        p={4}
                        bg="gray.50"
                        borderRadius="lg"
                        border="1px solid"
                        borderColor="gray.200"
                        textAlign="center"
                    >
                        <VStack gap={3}>
                            <Box
                                w="48px"
                                h="48px"
                                bg="gray.300"
                                borderRadius="full"
                                display="flex"
                                alignItems="center"
                                justifyContent="center"
                                mx="auto"
                            >
                                <FaHistory color="white" size="20px" />
                            </Box>
                            <VStack gap={1}>
                                <Text fontSize="md" fontWeight="medium" color="gray.700">
                                    No Bids Yet
                                </Text>
                                <Text fontSize="sm" color="gray.600">
                                    Be the first to place a bid on this auction!
                                </Text>
                            </VStack>
                        </VStack>
                    </Box>
                )}

                {/* User's Bid Status */}
                {userBid && (
                    <Box p={3} bg={userBid.isWinning ? "green.50" : "orange.50"} borderRadius="md">
                        <Text fontSize="sm" color={userBid.isWinning ? "green.700" : "orange.700"}>
                            {userBid.isWinning ? `🎉 ${t('Product.highestBidder')}` : `⚠️ ${t('Product.outbid')}`}
                        </Text>
                        <Text fontSize="sm" color="gray.600">
                            {t('Auction.bidAmount')}: <Box as="span" fontWeight="bold" color="gray.800">{formatPrice(convertPrice(userBid.amount, 'USD'), currency)}</Box>
                        </Text>
                    </Box>
                )}

                {/* Extended Bidding Info */}
                {extendedBiddingEnabled && auctionStatus === 'active' && (
                    <Box p={3} bg="blue.50" borderRadius="md" border="1px solid" borderColor="blue.200">
                        <VStack align="start" gap={1}>
                            <Text fontSize="sm" fontWeight="medium" color="blue.800">
                                <Icon as={FaSyncAlt} mr="2" /> Extended Bidding Enabled
                            </Text>
                            <Text fontSize="xs" color="blue.700">
                                Auction will be extended by {extendedBiddingDuration || 'N/A'} minutes if bids are placed in the final {extendedBiddingMinutes || 'N/A'} minutes.
                            </Text>
                        </VStack>
                    </Box>
                )}

                {/* Bid Placement */}
                {auctionStatus === 'active' && (
                    <VStack align="stretch" gap={4}>
                        {/* Bidding Mode Selection */}
                        <VStack align="stretch" gap={2}>
                            <Text fontSize="sm" fontWeight="medium" color="gray.700">
                                Choose Bidding Mode
                            </Text>
                            <HStack gap={4}>
                                <Button
                                    variant={biddingMode === 'bid_now' ? 'solid' : 'outline'}
                                    colorScheme="blue"
                                    size="sm"
                                    onClick={() => setBiddingMode('bid_now')}
                                    flex={1}
                                >
                                    Bid Now
                                </Button>
                                <Button
                                    variant={biddingMode === 'auto_bid' ? 'solid' : 'outline'}
                                    colorScheme="green"
                                    size="sm"
                                    onClick={() => setBiddingMode('auto_bid')}
                                    flex={1}
                                >
                                    Auto Bid
                                </Button>
                            </HStack>
                        </VStack>

                        {/* Bid Now Section */}
                        {biddingMode === 'bid_now' && (
                            <Box>
                                <Text fontSize="sm" color="gray.600" mb={2}>
                                    {t('Auction.minimumBid')}: <Box as="span" fontWeight="bold" color="gray.800">{formatPrice(convertPrice(minBidAmount, 'USD'))}</Box>
                                </Text>

                                {/* Auto-bid active warning */}
                                {isBidNowDisabled && (
                                    <Box p={3} bg="orange.50" borderRadius="md" border="1px solid" borderColor="orange.200" mb={3}>
                                        <Text fontSize="sm" color="orange.700" fontWeight="medium">
                                            🤖 Auto-bid is currently active. Manual bidding is disabled.
                                        </Text>
                                        <Text fontSize="xs" color="orange.600" mt={1}>
                                            Disable auto-bid to place manual bids.
                                        </Text>
                                    </Box>
                                )}

                                {
                                    isLoadingAuth ? (
                                        <Skeleton
                                            width="full"
                                            height="30px"
                                            borderRadius="lg" />

                                    ) :
                                        isAuthenticated ? (
                                            <HStack gap="4">
                                                <ChakraSelect
                                                    width='60%'
                                                    size='lg'
                                                    placeholder={t('Product.bidNow')}
                                                    collection={bidOptionsSelect}
                                                    lazyMount={true}
                                                    disabled={isBidNowDisabled}
                                                    onValueChange={handleBidAmountChange}
                                                />
                                                <Button
                                                    size={'lg'}
                                                    colorScheme="blue"
                                                    onClick={handlePlaceBid}
                                                    loading={placeBidMutation.isPending}
                                                    disabled={placeBidMutation.isPending || bidAmount < minBidAmount || isBidNowDisabled}
                                                    w="40%"
                                                    borderRadius={'full'}
                                                >
                                                    {t('Product.bidNow')}
                                                </Button>
                                            </HStack>
                                        ) : (
                                            <Button
                                                size={'lg'}
                                                colorScheme="blue"
                                                w="full"
                                                borderRadius={'full'}
                                                onClick={() => router.push('/auth/login')}
                                                disabled={isBidNowDisabled}
                                            >
                                                Login to Buy / Bid
                                            </Button>

                                        )
                                }

                                {bidAmount < minBidAmount && !isBidNowDisabled && (
                                    <Text fontSize="xs" color="red.500" mt={1}>
                                        {t('Auction.bidTooLow')}
                                    </Text>
                                )}
                            </Box>
                        )}

                        {/* Auto Extend Section */}
                        {biddingMode === 'auto_bid' && (
                            <Box>
                                <Text fontSize="sm" color="gray.600" mb={3}>
                                    Set your maximum budget and increment amount. When someone bids higher than you, the system will automatically place a bid on your behalf up to your maximum budget.
                                </Text>

                                {isAuthenticated ? (
                                    <VStack align="stretch" gap={3}>
                                        {/* Auto-bid Status */}
                                        {autoBidEnabled && userAutoBid && (
                                            <Box p={4} bg="green.50" borderRadius="lg" border="1px solid" borderColor="green.200">
                                                <VStack align="start" gap={2}>
                                                    <HStack>
                                                        <Icon color="green.600">
                                                            <FaSyncAlt />
                                                        </Icon>
                                                        <Text fontSize="sm" color="green.700" fontWeight="bold">
                                                            {t('Auction.autoBidActive')}
                                                        </Text>
                                                    </HStack>
                                                    <VStack align="start" gap={1} fontSize="xs" color="green.600">
                                                        <Text>
                                                            <strong>Current Bid:</strong> {formatPrice(convertPrice(currentBid, 'USD'), currency)}
                                                        </Text>
                                                        <Text>
                                                            <strong>Starting Bid:</strong> {formatPrice(convertPrice(userAutoBid.startingBid || currentBid + userAutoBid.bidIncrement, 'USD'), currency)}
                                                        </Text>
                                                        <Text>
                                                            <strong>Next Auto-bid:</strong> {formatPrice(convertPrice(Math.max(currentBid + userAutoBid.bidIncrement, userAutoBid.startingBid || currentBid + userAutoBid.bidIncrement), 'USD'), currency)}
                                                        </Text>
                                                        <Text>
                                                            <strong>Bid Increment:</strong> {formatPrice(convertPrice(userAutoBid.bidIncrement, 'USD'), currency)}
                                                        </Text>
                                                        <Text>
                                                            <strong>Max Budget:</strong> {formatPrice(convertPrice(userAutoBid.maxBudget, 'USD'), currency)}
                                                        </Text>
                                                        <Text>
                                                            <strong>Remaining Budget:</strong> {formatPrice(convertPrice(Math.max(0, userAutoBid.maxBudget - currentBid), 'USD'), currency)}
                                                        </Text>
                                                        <Text>
                                                            <strong>Status:</strong> {userAutoBid.isActive ? 'Active' : 'Inactive'}
                                                        </Text>
                                                        <Text color="gray.500">
                                                            Auto-bid will start from {formatPrice(convertPrice(userAutoBid.startingBid || currentBid + userAutoBid.bidIncrement, 'USD'), currency)} and place bids up to your max budget
                                                        </Text>
                                                    </VStack>
                                                </VStack>
                                            </Box>
                                        )}

                                        {/* Auto-bid Settings */}
                                        {!autoBidEnabled && (
                                            <VStack align="stretch" gap={3}>
                                                <Box>
                                                    <Text fontSize="xs" color="gray.600" mb={1}>Starting Bid Amount</Text>
                                                    <ChakraSelect
                                                        size='md'
                                                        placeholder={formatPrice(convertPrice(startingBid, 'USD'), currency)}
                                                        collection={autoBidStartingBidSelect}
                                                        lazyMount={true}
                                                        onValueChange={handleStartingBidChange}
                                                    />
                                                    <Text fontSize="xs" color="gray.500" mt={1}>
                                                        Auto-bid will start placing bids from this amount
                                                    </Text>
                                                </Box>

                                                <Box>
                                                    <Text fontSize="xs" color="gray.600" mb={1}>{t('Auction.maxBudget')}</Text>
                                                    <ChakraSelect
                                                        size='md'
                                                        placeholder={formatPrice(convertPrice(maxBudget, 'USD'), currency)}
                                                        collection={autoBidMaxBudgetSelect}
                                                        lazyMount={true}
                                                        onValueChange={handleMaxBudgetChange}
                                                    />
                                                </Box>

                                                <Box>
                                                    <Text fontSize="xs" color="gray.600" mb={1}>{t('Auction.bidIncrementAmount')}</Text>
                                                    <ChakraSelect
                                                        size='md'
                                                        placeholder={formatPrice(convertPrice(bidIncrement, 'USD'), currency)}
                                                        collection={bidIncrementSelect}
                                                        lazyMount={true}
                                                        onValueChange={handleBidIncrementChange}
                                                    />
                                                </Box>

                                                <Button
                                                    size='lg'
                                                    colorScheme="green"
                                                    onClick={handleEnableAutoBid}
                                                    loading={autoBidMutation.isPending}
                                                    disabled={autoBidMutation.isPending || maxBudget <= currentBid}
                                                    borderRadius='full'
                                                >
                                                    {t('Auction.enableAutoBid')}
                                                </Button>
                                            </VStack>
                                        )}

                                        {/* Disable Auto-bid */}
                                        {autoBidEnabled && (
                                            <Button
                                                size='md'
                                                variant="outline"
                                                colorScheme="red"
                                                onClick={handleDisableAutoBid}
                                                loading={cancelAutoBidMutation.isPending}
                                                disabled={cancelAutoBidMutation.isPending}
                                                borderRadius='full'
                                            >
                                                {t('Auction.disableAutoBid')}
                                            </Button>
                                        )}
                                    </VStack>
                                ) : (
                                    <Button
                                        size='lg'
                                        colorScheme="green"
                                        w="full"
                                        borderRadius='full'
                                        onClick={() => router.push('/auth/login')}
                                    >
                                        Login to Enable Auto-bid
                                    </Button>
                                )}
                            </Box>
                        )}
                    </VStack>
                )}

                {/* Auction Ended Message */}
                {auctionStatus === 'ended' && (
                    <Box p={4} bg="gray.100" borderRadius="md" textAlign="center">
                        <Text fontWeight="extrabold" color="gray.800">
                            Auction has ended
                        </Text>
                        {
                            bidHistory && bidHistory.bids.length > 0 ? (
                                <Text fontSize="md" color="gray.700" fontWeight="semibold" >
                                    Final bid: <Box as="span" me={2} fontSize={24} fontWeight="bold" color="green.600">{formatPrice(convertPrice(currentBid, 'USD'), currency)}</Box>
                                </Text>
                            ) : (
                                <Text fontSize="md" color="red.600">
                                    No bids were placed
                                </Text>
                            )
                        }
                        {userBid?.isWinning && (
                            <Text fontSize="sm" color="green.600" fontWeight="bold" mt={2}>
                                🎉 Congratulations! You won this auction!
                            </Text>
                        )}
                    </Box>
                )}

                {auctionStatus === 'upcoming' && (
                    <Box p={4} bg="blue.50" borderRadius="md" textAlign="center">
                        <Text fontWeight="bold" color="blue.700">
                            Auction hasn't started yet
                        </Text>
                        <Text fontSize="sm" color="gray.800">
                            {t('Product.startingBid')}: {formatPrice(convertPrice(startingPrice, 'USD'), currency)}
                        </Text>
                    </Box>
                )}

                <HStack gap={2} mt={4}>
                    {/* <Button
                        variant="ghost"
                        size="sm"
                        onClick={handleAddToWishlist}
                        color={isInWishlist ? "red.500" : "gray.600"}
                        _hover={{
                            color: isInWishlist ? "red.600" : "red.500",
                            bg: isInWishlist ? "red.50" : "gray.50"
                        }}
                        flex={1}
                    >
                        <FaHeart />
                        {isInWishlist ? 'Remove from Watchlist' : 'Add to Watchlist'}
                    </Button> */}
                </HStack>

                {/* Auction Info */}
                {/* <Box pt={4} borderTop="1px solid" borderColor="gray.100">
                    <Text fontSize="xs" color="gray.500">
                        • Bid increments: {formatUSD(minBidIncrement)} minimum
                    </Text>
                    <Text fontSize="xs" color="gray.500">
                        • Winner pays within 48 hours
                    </Text>
                    <Text fontSize="xs" color="gray.500">
                        • Secure payment processing
                    </Text>
                </Box> */}
            </VStack>
            {/* <BidHistoryModal
                isOpen={true}
                onClose={() => setBidHistoryOpen({
                    open: false, 
                    product: undefined
                })}
                productId={bidHistoryOpen.product.id}
                productName={bidHistoryOpen.product?.itemName}
            /> */}
        </Box>
    );
};

export default AuctionInfo;
