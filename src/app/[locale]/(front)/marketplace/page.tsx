'use client';

import ProductCard from '@/components/product/ProductCard';
import { ChakraSelect } from '@/components/ui/select/ChakraSelect';
import {
    Box,
    Grid,
    GridItem,
    Heading,
    VStack,
    HStack,
    Text,
    Input,
    InputGroup,
    Checkbox,
    CheckboxGroup,
    SimpleGrid,
    createListCollection,
    Tabs,
    Fieldset,
    Icon,
    Skeleton,
    Button
} from '@chakra-ui/react';
import { useState, useEffect } from 'react';
import { FaTimesCircle } from 'react-icons/fa';
import { useRouter, useSearchParams } from 'next/navigation';
import { useProductsQuery, useCategoriesQuery, ProductQueryParams, PriceRangeState, SellType, SortField, SortOrder } from '@/services/useProductQuery';
import { ProductItem } from '@/types/product';
import { getTimeLeftString } from '@/utils/helpers/helper';
import { useCurrencyLanguage } from '@/contexts/CurrencyLanguageContext';
import { isDatePastJakarta } from '@/utils/timezone';

const convertToProductItem = (product: any, formatPrice: (amount: number) => string, convertPrice: (amount: number, fromCurrency: 'USD' | 'IDR') => number): ProductItem => {
    const mainImage = product.images?.find((img: any) => img.isMain) || product.images?.[0];

    // Convert price from USD to current currency
    const basePrice = Number(product.currentBid ?? product.priceUSD);
    const convertedPrice = convertPrice(basePrice, 'USD');
    const formattedPrice = formatPrice(convertedPrice);

    console.log(`Marketplace: ${product.itemName} - Original: ${basePrice} USD, Converted: ${convertedPrice}, Formatted: ${formattedPrice}`);

    return {
        id: product.id,
        image: mainImage?.imageUrl || '/assets/images/placeholder.png',
        images: product.images?.map((img: any) => img.imageUrl) || [],
        title: product.itemName,
        price: formattedPrice,
        bids: product.bidCount?.toString() || '0',
        slug: product.slug,
        type: product.sellType,
        timeLeft: product.auctionEndDate ?
            !isDatePastJakarta(product.auctionEndDate) ?
                `${getTimeLeftString(product.auctionEndDate)}`
                : 'Ended'
            : undefined,
    };
};

const AllProductsPage = () => {
    const searchParams = useSearchParams();
    const router = useRouter();
    const { formatPrice, convertPrice } = useCurrencyLanguage();
    const [priceRange, setPriceRange] = useState<PriceRangeState>({
        min: searchParams.get('minPrice') || '',
        max: searchParams.get('maxPrice') || ''
    });

    const getFiltersFromParams = (): ProductQueryParams => {
        const params = new URLSearchParams(searchParams.toString());

        // Helper to parse number or return undefined
        const parsePrice = (value: string | null): number | undefined => {
            if (!value) return undefined;
            const num = parseFloat(value);
            return isNaN(num) ? undefined : num;
        };

        console.log("params.get('sellType')", params.get('sellType'))

        return {
            sellType: (params.get('sellType') as SellType) || '',
            category: params.get('category') || '',
            minPrice: parsePrice(params.get('minPrice')),
            maxPrice: parsePrice(params.get('maxPrice')),
            sortBy: (params.get('sortBy') as SortField) || 'createdAt',
            sortOrder: (params.get('sortOrder') as SortOrder) || 'desc',
            page: Math.max(1, parseInt(params.get('page') || '1')),
            keyword: params.get('keyword') || '',
            limit: 12
        };
    };

    const [filters, setFilters] = useState<ProductQueryParams>(getFiltersFromParams());

    useEffect(() => {
        setFilters(getFiltersFromParams());
    }, [searchParams]);

    const queryParams = {
        page: filters.page,
        limit: 12,
        ...(filters.category && { categoryId: filters.category }),
        ...(filters.sellType && { sellType: filters.sellType }),
        sortBy: filters.sortBy,
        sortOrder: filters.sortOrder,
        search: filters.keyword,
        status: 'active',
        ...(filters.minPrice && { minPrice: filters.minPrice }),
        ...(filters.maxPrice && { maxPrice: filters.maxPrice })
    };

    const { data: categories, isLoading: categoriesLoading } = useCategoriesQuery();
    const { data: productsData, isLoading: productsLoading, error: productsError } = useProductsQuery(queryParams);

    const updateUrlParams = (newFilters: Partial<typeof filters>) => {
        const params = new URLSearchParams(searchParams.toString());

        if (!newFilters.page) newFilters.page = 1;

        Object.entries(newFilters).forEach(([key, value]) => {
            if (value === '' || value === null || value === undefined) {
                params.delete(key);
            } else {
                params.set(key, value.toString());
            }
        });

        router.push(`/marketplace?${params.toString()}`);
    };

    const handleSortChange = (details: any) => {
        if (details.value && details.value.length > 0) {
            const [sortBy, order] = details.value[0].split('-');
            updateUrlParams({ sortBy, sortOrder: order });
        }
    };

    const handleSellTypeChange = (value: ProductQueryParams['sellType']) => {
        updateUrlParams({ sellType: value === 'all' ? '' : value });
    };

    const handleCategoryChange = (categories: string[]) => {
        updateUrlParams({ category: categories[0] || '' });
    };

    const handlePriceRangeChange = (type: 'min' | 'max', value: string) => {
        setPriceRange(prev => ({
            ...prev,
            [type]: value
        }));
    };

    const clearFilter = (filterType: keyof typeof filters) => {
        updateUrlParams({ [filterType]: '' });
    };

    const handleEnterPriceRange = (e: React.KeyboardEvent<HTMLInputElement>) => {
        if (e.key === 'Enter') {
            e.preventDefault();
            const minPrice = parseFloat(priceRange.min) || 0;
            const maxPrice = parseFloat(priceRange.max) || undefined;
            updateUrlParams({
                minPrice: minPrice,
                maxPrice: maxPrice
            });
        }
    };

    const products = productsData?.products?.map(product => convertToProductItem(product, formatPrice, convertPrice)) || [];
    const totalProducts = productsData?.pagination?.total || 0;
    const totalPages = productsData?.pagination?.totalPages || 1;

    if (categoriesLoading || productsLoading) {
        return (
            <Box p={{ base: 4, md: 6 }} mx="auto">
                <Grid templateColumns={{ base: '1fr', md: '300px 1fr' }} gap={6}>
                    <GridItem>
                        <Skeleton height="600px" borderRadius="md" />
                    </GridItem>
                    <GridItem>
                        <Skeleton height="60px" mb={6} borderRadius="md" />
                        <SimpleGrid columns={{ base: 1, sm: 2, lg: 3, xl: 3 }} gap={4}>
                            {Array.from({ length: 12 }).map((_, index) => (
                                <Skeleton key={index} height="400px" borderRadius="md" />
                            ))}
                        </SimpleGrid>
                    </GridItem>
                </Grid>
            </Box>
        );
    }

    if (productsError) {
        return (
            <Box p={{ base: 4, md: 6 }} mx="auto">
                <Box bg="red.50" border="1px solid" borderColor="red.200" borderRadius="md" p={4}>
                    <Text color="red.600" fontWeight="bold" mb={2}>
                        Error loading products!
                    </Text>
                    <Text color="red.500" fontSize="sm">
                        Failed to load products. Please try again later.
                    </Text>
                </Box>
            </Box>
        );
    }

    return (
        <Box p={{ base: 4, md: 6 }} mx="auto">
            <Grid templateColumns={{ base: '1fr', md: '300px 1fr' }} gap={6}>
                <GridItem
                    zIndex={1}
                    bg="white"
                    p={4}
                    borderRadius={'md'}
                    display="inline-block"
                    height={'fit-content'}
                    position={{
                        base: 'static',
                        lg: 'sticky'
                    }}
                    top="100px"
                >
                    <VStack align="stretch" gap={6} mb={6} display="flex">
                        <Text fontSize="xl" fontWeight="bold">
                            Filter
                        </Text>
                        <Tabs.Root
                            value={filters.sellType || 'all'}
                            onValueChange={(value) => {
                                handleSellTypeChange(value.value as ProductQueryParams['sellType']);
                            }}
                            variant="plain"
                        >
                            <Tabs.List bg="bg.emphasized" rounded="3xl" p="1" whiteSpace={"nowrap"}>
                                <Tabs.Trigger value="all">
                                    All
                                </Tabs.Trigger>
                                <Tabs.Trigger value="auction">
                                    Auction
                                </Tabs.Trigger>
                                <Tabs.Trigger value="buy-now">
                                    Buy Now
                                </Tabs.Trigger>
                                <Tabs.Indicator rounded="3xl" />
                            </Tabs.List>
                        </Tabs.Root>
                        <Box>
                            <Heading size="sm" mb={2}>Price Range</Heading>
                            <HStack as={'form'} onSubmit={(e) => {
                                e.preventDefault();

                            }} gap={2}>
                                <InputGroup startElement="$">
                                    <Input
                                        placeholder="Min"
                                        type="number"
                                        value={priceRange.min}
                                        onChange={(e) => handlePriceRangeChange('min', e.target.value)}
                                        onKeyDown={handleEnterPriceRange}
                                    />
                                </InputGroup>
                                <InputGroup startElement="$">
                                    <Input
                                        placeholder="Max"
                                        type="number"
                                        value={priceRange.max}
                                        onChange={(e) => handlePriceRangeChange('max', e.target.value)}
                                        onKeyDown={handleEnterPriceRange}
                                    />
                                </InputGroup>
                            </HStack>
                            <Text fontSize="xs" color="gray.500" mt={2}>
                                Enter/Submit to apply price range
                            </Text>
                        </Box>
                        <Fieldset.Root>
                            <CheckboxGroup
                                value={filters.category ? [filters.category] : []}
                                onValueChange={handleCategoryChange}
                                name="categories"
                            >
                                <Fieldset.Legend fontSize="sm" mb="2">
                                    <Text fontSize="sm" fontWeight="semibold">Category</Text>
                                </Fieldset.Legend>
                                <Fieldset.Content>
                                    {categories?.map((category) => (
                                        <Checkbox.Root key={category.id} value={category.id}>
                                            <Checkbox.HiddenInput />
                                            <Checkbox.Control />
                                            <Checkbox.Label>{category.name}</Checkbox.Label>
                                        </Checkbox.Root>
                                    ))}
                                </Fieldset.Content>
                            </CheckboxGroup>
                        </Fieldset.Root>
                    </VStack>
                </GridItem>

                <GridItem>
                    <Box mb={6}>
                        <HStack justify="space-between" alignItems="center">
                            <Box>
                                <Text fontSize="sm" fontWeight="bold">
                                    Showing {(((filters?.page ?? 0) - 1) * 12) + 1} - {Math.min((filters?.page ?? 0) * 12, totalProducts)} of {totalProducts}
                                </Text>
                            </Box>
                            <HStack>
                                <Text fontSize="sm" fontWeight="bold">
                                    Sort By :
                                </Text>
                                <ChakraSelect
                                    placeholder="Select sorting"
                                    collection={createListCollection({
                                        items: [
                                            { value: "createdAt-desc", label: "Newest First" },
                                            { value: "createdAt-asc", label: "Oldest First" },
                                            { value: "priceUSD-asc", label: "Price: Low to High" },
                                            { value: "priceUSD-desc", label: "Price: High to Low" },
                                            { value: "bidCount-desc", label: "Most Bids" },
                                            { value: "auctionEndDate-asc", label: "Ending Soonest" }
                                        ],
                                        itemToValue: (item) => item.value,
                                    })}
                                    defaultValue={[`${filters.sortBy}-${filters.sortOrder}`]}
                                    onValueChange={handleSortChange}
                                />
                            </HStack>
                        </HStack>
                        <HStack alignItems={'center'} gap={2} flexWrap="wrap">
                            {/* Price Range Filter */}
                            {(filters.minPrice || filters.maxPrice) && (
                                <Box
                                    borderWidth={1}
                                    borderColor="gray.300"
                                    borderStyle="solid"
                                    borderRadius="lg"
                                    px={3}
                                    py={2}
                                    display="flex"
                                    alignItems="center"
                                    gap={2}
                                    bg="white"
                                >
                                    <Text fontSize="sm" color="gray.800">
                                        Price: ${filters.minPrice || '0'} - ${filters.maxPrice || '∞'}
                                    </Text>
                                    <Icon
                                        as={FaTimesCircle}
                                        boxSize={4}
                                        color="gray.600"
                                        cursor="pointer"
                                        onClick={() => {
                                            updateUrlParams({ minPrice: 0, maxPrice: undefined });
                                            handlePriceRangeChange('min', '');
                                            handlePriceRangeChange('max', '');
                                        }}
                                    />
                                </Box>
                            )}

                            {/* Category Filter */}
                            {filters.category && (
                                <Box
                                    borderWidth={1}
                                    borderColor="gray.300"
                                    borderStyle="solid"
                                    borderRadius="lg"
                                    px={3}
                                    py={2}
                                    display="flex"
                                    alignItems="center"
                                    gap={2}
                                    bg="white"
                                >
                                    <Text fontSize="sm" color="gray.800">
                                        Category: {categories?.find(c => c.id === filters.category)?.name}
                                    </Text>
                                    <Icon
                                        as={FaTimesCircle}
                                        boxSize={4}
                                        color="gray.600"
                                        cursor="pointer"
                                        onClick={() => clearFilter('category')}
                                    />
                                </Box>
                            )}

                            {/* Sell Type Filter */}
                            {filters.sellType && (
                                <Box
                                    borderWidth={1}
                                    borderColor="gray.300"
                                    borderStyle="solid"
                                    borderRadius="lg"
                                    px={3}
                                    py={2}
                                    display="flex"
                                    alignItems="center"
                                    gap={2}
                                    bg="white"
                                >
                                    <Text fontSize="sm" color="gray.800">
                                        Type: {filters.sellType === 'auction' ? 'Auction' : 'Buy Now'}
                                    </Text>
                                    <Icon
                                        as={FaTimesCircle}
                                        boxSize={4}
                                        color="gray.600"
                                        cursor="pointer"
                                        onClick={() => clearFilter('sellType')}
                                    />
                                </Box>
                            )}

                            {filters.keyword && (
                                <Box
                                    borderWidth={1}
                                    borderColor="gray.300"
                                    borderStyle="solid"
                                    borderRadius="lg"
                                    px={3}
                                    py={2}
                                    display="flex"
                                    alignItems="center"
                                    gap={2}
                                    bg="white"
                                >
                                    <Text fontSize="sm" color="gray.800">
                                        Search: {filters.keyword}
                                    </Text>
                                    <Icon
                                        as={FaTimesCircle}
                                        boxSize={4}
                                        color="gray.600"
                                        cursor="pointer"
                                        onClick={() => clearFilter('keyword')}
                                    />
                                </Box>
                            )}
                        </HStack>
                    </Box>

                    <Box>
                        {products.length === 0 ? (
                            <Box textAlign="center" py={12}>
                                <Text fontSize="lg" color="gray.600" mb={2}>
                                    No products found
                                </Text>
                                <Text fontSize="sm" color="gray.500">
                                    Try adjusting your filters or search criteria
                                </Text>
                            </Box>
                        ) : (
                            <>
                                <SimpleGrid alignItems={'start'} columns={{ base: 1, sm: 2, lg: 3, xl: 3 }} gapX={4} gapY={2}>
                                    {products.map(item => (
                                        <GridItem key={item.id} mb={4}>
                                            <ProductCard
                                                item={item}
                                                backgroundColorCardImage="none"
                                                containerProps={{
                                                    maxW: 'full',
                                                    maxH: 'full',
                                                    backgroundColor: 'white',
                                                    px: 4,
                                                    pb: 6,
                                                    borderRadius: 'md',
                                                }}
                                                imageContainerProps={{
                                                    aspectRatio: '1/1',
                                                }}
                                                bodyContainerProps={{
                                                    borderTopWidth: 1,
                                                    borderTopColor: 'gray.200',
                                                    borderTopStyle: 'solid',
                                                    pt: 4,
                                                }}
                                                imageProps={{ objectFit: 'cover' }}
                                            />
                                        </GridItem>
                                    ))}
                                </SimpleGrid>

                                {/* Pagination */}
                                {totalPages > 1 && (
                                    <HStack justify="center" mt={8} gap={2}>
                                        <Button
                                            variant="outline"
                                            size="sm"
                                            onClick={() => updateUrlParams({ page: (filters?.page ?? 0) - 1 })}
                                            disabled={(filters?.page ?? 0) === 1}
                                        >
                                            Previous
                                        </Button>

                                        {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                                            const pageNum = (filters?.page ?? 0) <= 3 ? i + 1 : (filters?.page ?? 0) - 2 + i;
                                            if (pageNum > totalPages) return null;

                                            return (
                                                <Button
                                                    key={pageNum}
                                                    variant={(filters?.page ?? 0) === pageNum ? "solid" : "outline"}
                                                    size="sm"
                                                    onClick={() => updateUrlParams({ page: pageNum })}
                                                >
                                                    {pageNum}
                                                </Button>
                                            );
                                        })}

                                        <Button
                                            variant="outline"
                                            size="sm"
                                            onClick={() => updateUrlParams({ page: (filters?.page ?? 0) + 1 })}
                                            disabled={(filters?.page ?? 0) === totalPages}
                                        >
                                            Next
                                        </Button>
                                    </HStack>
                                )}
                            </>
                        )}
                    </Box>
                </GridItem>
            </Grid>
        </Box>
    );
};

export default AllProductsPage;