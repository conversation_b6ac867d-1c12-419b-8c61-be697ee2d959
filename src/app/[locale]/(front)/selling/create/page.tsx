import FormSelling from '@/components/selling/FormSelling'
import SellerVerificationGuard from '@/components/account/SellerVerificationGuard'
import { Container } from '@chakra-ui/react'
import React from 'react'

export default function CreateSellingPage() {
  return (
    <SellerVerificationGuard>
      <Container
        minH="100vh"
        maxW={{
          base: "full",
          lg: "8/12"
        }}
        display={"flex"}
        py={8}>
        <FormSelling />
      </Container>
    </SellerVerificationGuard>
  )
}
